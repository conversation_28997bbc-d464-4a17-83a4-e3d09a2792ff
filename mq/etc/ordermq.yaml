Name: order-mq

Log:
  ServiceName: OrdercliApiLogger
  Mode: console
  Path: /home/<USER>/logs/Ordercli/api
  Level: info
  Compress: false
  Encoding: plain
  KeepDays: 7
  StackCoolDownMillis: 100




RocketConsumerConf:
  AccessKey: "LTAI5tDDTDGR7XcfmmSQmaqY"
  AccessSecret: "******************************"
  Endpoint: "http://1694942017801008.mqrest.cn-shanghai.aliyuncs.com"
  InstanceId: "MQ_INST_1694942017801008_BZWhwLgi"
  Topics:
    OrderTopic: "order"
    OrderSettlementTopic: "order_settlement"
    HlhtOrderTopic: "open_order"
  Groups:
    - Group_id: "GID_Devices"
      Topics: trade
      Name: "trade"
    - Group_id: "GID_Devices"
      Topics: order
      Name: "order"
    - Group_id: "GID_Devices"
      Topics: start_charge
      Name: "start_charge"
    - Group_id: "GID_open"
      Topics: order_settlement
      Name: "order_settlement"
    - Group_id: "GID_Devices"
      Topics: xiaojing_gun_status
      Name: "gun_status"
    - Group_id: "GID_Devices"
      Topics: xiaojing_order_rollback
      Name: "order_rollback"

RedisConf:
  Host: *************:6379

DatabaseConf:
  Type: mysql
  Host: *************
  Port: 3306
  DBName: simple_admin
  Username: root
  Password: simple-admin.
  MaxOpenConn: 100
  SSLMode: disable
  CacheTime: 5

TdConf:
  DBType: "tdengine"
  Driver: "taosRestful"
  DSN: "root:C44E2oeJcZq@http(**************:6041)/electricity?readBufferSize=52428800"
  DBName: "electricity"

EtcdServer:
  Endpoints:
    - "http://*************:2379"

BusinessConf:
  AutoSettlement: true # 是否开启自动结算
  AutoSettlementTime: 0 # 自动结算时间，单位分钟