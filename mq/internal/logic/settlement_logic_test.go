package logic

import (
	"com.xiaojing.order_service/mq/internal/config"
	"com.xiaojing.order_service/mq/internal/svc"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"testing"
	"time"
)

func TestSettlementLogic_Handle(t *testing.T) {
	var c config.Config

	conf.MustLoad("./../../etc/ordermq.yaml", &c)
	if err := c.SetUp(); err != nil {
		panic(err)
	}

	svcCtx := svc.NewServiceContext(c)

	mqProducer := svcCtx.RocketMqClient.GetProducer(svcCtx.Config.RocketConsumerConf.InstanceId, svcCtx.Config.RocketConsumerConf.Topics.OrderSettlementTopic)

	m := MqttMessageAckOrder{
		OrderNo: "17489992711451930068949416808448",
	}

	body, err := jsonx.MarshalToString(&m)
	if err != nil {
		logx.Errorf("MqttMessageAckOrder 序列化失败err:%v", err)
	}

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: body, //消息内容
	}

	// 设置延时订单结算消息时间
	msg.StartDeliverTime = time.Now().Add(time.Duration(svcCtx.Config.BusinessConf.AutoSettlementTime) * time.Minute).UnixMilli()

	_, err = mqProducer.PublishMessage(msg)
	if err != nil {
		logx.Errorf("生产者订单结算消息topic:%s, 发送失败：err:%v", svcCtx.Config.RocketConsumerConf.Topics.OrderSettlementTopic, err)
		return
	}
}
