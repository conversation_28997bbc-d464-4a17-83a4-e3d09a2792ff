package logic

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"com.xiaojing.order_service/internal/types"
	"com.xiaojing.order_service/mq/internal/config"
	"com.xiaojing.order_service/mq/internal/svc"
	"context"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type OrderRollbackLogic struct {
	c      config.Config
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

func NewOrderRollbackLogic(c config.Config, svcCtx *svc.ServiceContext) *OrderRollbackLogic {
	return &OrderRollbackLogic{
		c:      c,
		svcCtx: svcCtx,
		ctx:    context.Background(),
	}
}

func (l *OrderRollbackLogic) Handle(data string) error {
	orderInfo, err := l.svcCtx.DB.Order.Query().Where(order.OrderNo(data)).First(l.ctx)
	if err != nil {
		logx.Errorf("订单号：%s,查询失败不存在err：%v", data, err)
		return err
	}

	// 订单充电状态不为启动中或者非内部订单，直接返回
	if orderInfo.ChargingStatus != consts.ChargingStatusStarting || orderInfo.Source != consts.OrderSourceInner {
		return nil
	}

	err = l.Refund(orderInfo, orderInfo.TotalAmount)
	if err != nil {
		return err
	}

	return nil
}

func (l *OrderRollbackLogic) Refund(orderInfo *ent.Order, refundMoney float64) error {
	req := types.AutoRefundReq{
		OrderId:      orderInfo.ID,
		Reason:       "充电桩启动失败退款",
		RefundAmount: int64(refundMoney * 100),
	}
	b, err := jsonx.Marshal(req)
	if err != nil {
		return err
	}
	resp, err := httpc.RequestService[types.RefundInfo](l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.PayService, "POST", "/v1/api/payment/refund", nil, b)
	if err != nil {
		logx.Errorw("调用退款服务失败", logx.Field("err", err), logx.Field("orderid", orderInfo.OrderNo))
		return err
	}

	//微信退款状态
	if resp.Status == 4 {
		//退款成功, 更新订单状态为结束
		return l.svcCtx.DB.Order.UpdateOneID(orderInfo.ID).
			SetRefundTime(time.Now()).
			SetPaymentStatus(consts.PayStatusRefund).
			SetRefundAmount(refundMoney).
			Exec(l.ctx)

	} else {
		//退款失败, 记录退款金额，没有时间
		err = l.svcCtx.DB.Order.UpdateOneID(orderInfo.ID).
			SetRefundAmount(refundMoney).
			Exec(l.ctx)
		if err != nil {
			logx.Errorf("退款失败err:%v", err)
		}
		logx.Errorf("退款失败,订单号:%s,退款金额:%f,失败err:%v", orderInfo.OrderNo, refundMoney, err)
		return errors.New("退款失败")
	}
}
