package logic

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"com.xiaojing.order_service/internal/types"
	"com.xiaojing.order_service/mq/internal/config"
	"com.xiaojing.order_service/mq/internal/svc"
	"context"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

type SettlementLogic struct {
	c      config.Config
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

func NewSettlementLogic(c config.Config, svcCtx *svc.ServiceContext) *SettlementLogic {
	return &SettlementLogic{
		c:      c,
		svcCtx: svcCtx,
	}
}

func (l *SettlementLogic) Handle(data string) error {
	logx.Infof("结算通知msg:%s", data)
	l.ctx = context.Background()
	var msg MqttMessageAckOrder
	err := jsonx.UnmarshalFromString(data, &msg)
	if err != nil {
		logx.Error(err)
		return err
	}

	orderInfo, err := l.svcCtx.DB.Order.Query().Where(order.OrderNo(msg.OrderNo)).First(l.ctx)
	if err != nil {
		logx.Errorf("订单号：%s,查询失败不存在err：%v", msg.OrderNo, err)
		return err
	}

	// 已结算或者订单不为已结束直接返回
	if orderInfo.SettlementStatus == 1 || orderInfo.OrderStatus != consts.OrderStatusOk {
		return nil
	}

	if orderInfo.RefundAmount > 0 {
		err = l.Refund(orderInfo)
		if err != nil {
			logx.Errorf("订单号：%s,退款失败err：%v", msg.OrderNo, err)
			return err
		}
	} else {
		err = l.svcCtx.DB.Order.UpdateOneID(msg.OrderNo).
			SetSettlementStatus(1).
			SetSettlementTime(time.Now()).
			Exec(l.ctx)
		if err != nil {
			logx.Errorf("订单号：%s,更新订单状态失败err：%v", msg.OrderNo, err)
			return err
		}
	}

	return nil
}

func (l *SettlementLogic) Refund(orderInfo *ent.Order) error {
	req := types.AutoRefundReq{
		OrderId:      orderInfo.ID,
		Reason:       "订单自动结算退款",
		RefundAmount: int64(orderInfo.RefundAmount * 100),
	}
	b, err := jsonx.Marshal(req)
	if err != nil {
		return err
	}
	resp, err := httpc.RequestService[types.RefundInfo](l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.PayService, "POST", "/v1/api/payment/refund", nil, b)
	if err != nil {
		logx.Errorw("调用退款服务失败", logx.Field("err", err), logx.Field("orderid", orderInfo.OrderNo))
		return err
	}

	logx.Infof("退款返回msg:%v", resp)

	//微信退款状态
	if resp.Status == 4 {
		//退款成功, 更新订单状态为结束
		return l.svcCtx.DB.Order.UpdateOneID(orderInfo.ID).
			SetRefundTime(time.Now()).
			SetSettlementStatus(1).
			SetSettlementTime(time.Now()).
			Exec(l.ctx)

	} else {
		//退款失败, 记录日志
		logx.Errorf("退款失败,订单号:%s,退款金额:%f,失败err:%v", orderInfo.OrderNo, orderInfo.RefundAmount, err)
		return errors.New("退款失败")
	}
}
