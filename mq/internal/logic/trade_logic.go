package logic

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"com.xiaojing.order_service/internal/types"
	"com.xiaojing.order_service/mq/internal/config"
	"com.xiaojing.order_service/mq/internal/svc"
	"com.xiaojing.order_service/pkg/tdengine"
	"context"
	"database/sql"
	"errors"
	"fmt"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/threading"
	"sort"
	"time"
)

type pair struct {
	startMinutes int
	tpl          types.PriceTemplateList
}

type MqttMessageAckTrade struct {
	MsgID     int
	Topic     string
	DeviceID  string
	Operation string
	Timestamp int64
	Data      *TradeInfo `json:"Data"`
}

type AutoRefundReq struct {
	OrderId      string `json:"orderId"`
	Reason       string `json:"reason"`
	RefundAmount int64  `json:"refundAmount"`
}

type RefundInfo struct {
	RefundId     string `json:"refundId"`
	RefundNo     string `json:"refundNo"`
	RefundAmount int64  `json:"refundAmount"`
	Status       int8   `json:"status"`
}

type TradeInfo struct {
	TradeID      string    // 交易流水号
	PileCode     string    // 桩编号
	GunNo        int       // 枪号
	StartTime    time.Time // 开始时间
	EndTime      time.Time // 结束时间
	PeakPrice    float64   // 尖单价
	PeakEnergy   float64   // 尖电量
	PeakLoss     float64   // 计损尖电量
	PeakAmount   float64   // 尖金额
	ValleyPrice  float64   // 峰单价
	ValleyEnergy float64   // 峰电量
	ValleyLoss   float64   // 计损峰电量
	ValleyAmount float64   // 峰金额
	FlatPrice    float64   // 平单价
	FlatEnergy   float64   // 平电量
	FlatLoss     float64   // 计损平电量
	FlatAmount   float64   // 平金额
	TroughPrice  float64   // 谷单价
	TroughEnergy float64   // 谷电量
	TroughLoss   float64   // 计损谷电量
	TroughAmount float64   // 谷金额
	MeterStart   float64   // 电表总起值
	MeterEnd     float64   // 电表总止值
	TotalEnergy  float64   // 总电量
	TotalLoss    float64   // 计损总电量
	TotalAmount  float64   // 消费金额
	VIN          string    // VIN码
	TradeType    int       // 交易标识  1.app启动 2.卡启动 3.离线卡启动 4.vin码启动
	TradeTime    time.Time // 交易日期、时间
	StopReason   int       // 停止原因 没有说明
	PhysicalCard string    // 物理卡号
}

type GunPriceReq struct {
	DeviceNo string `json:"deviceNo"`
	GunNo    string `json:"gunNo"`
	UserId   string `json:"userId"`
}

type TradeLogic struct {
	c      config.Config
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

func NewTradeLogic(c config.Config, svcCtx *svc.ServiceContext) *TradeLogic {
	return &TradeLogic{
		c:      c,
		svcCtx: svcCtx,
	}
}

func (l *TradeLogic) Handle(data string) error {
	logx.Infof("trade 接收消息：%s", data)
	l.ctx = context.Background()
	mqttMessage := MqttMessageAckTrade{}
	err := jsonx.UnmarshalFromString(data, &mqttMessage)
	if err != nil {
		logx.Error(err)
		return err
	}

	orderInfo, err := l.svcCtx.DB.Order.Query().Where(order.ID(mqttMessage.Data.TradeID)).First(l.ctx)
	if err != nil {
		logx.Errorf("订单号：%s,查询失败不存在err：%v", mqttMessage.Data.TradeID, err)
		return err
	}

	if orderInfo.OrderStatus == consts.OrderStatusOk || orderInfo.OrderStatus == consts.OrderStatusCancel || orderInfo.OrderStatus == consts.OrderStatusRefund {
		//已处理直接结束
		return nil
	}

	err = l.insertTrade(mqttMessage)
	if err != nil {
		return err
	}

	//结束充电
	if orderInfo.OrderStatus != consts.OrderStatusServicing || (orderInfo.ChargingStatus != consts.ChargingStatusEnding && orderInfo.ChargingStatus != consts.ChargingStatusCharging) {
		logx.Errorf("结束订单失败， 订单号：%s,订单状态错误order:%v", orderInfo.OrderNo, orderInfo)
		return nil
	}

	duration := mqttMessage.Data.EndTime.Sub(orderInfo.StartTime).Minutes()

	//开启事务
	tx, err := l.svcCtx.DB.Tx(l.ctx)
	if err != nil {
		logx.Errorf("订单号：%s,开启事务失败err：%v", orderInfo.OrderNo, err)
		return err
	}

	refundMoney, err := l.RecodeOrder(tx, orderInfo, duration)
	if err != nil {
		_ = tx.Rollback()
		return err
	}

	if orderInfo.Source == consts.OrderSourceOperate {
		threading.GoSafe(func() {
			l.notifyHLHT(refundMoney, orderInfo, mqttMessage.Data.EndTime)
		})
	}

	// 开启自动结算且为内部订单
	if l.svcCtx.Config.BusinessConf.AutoSettlement && orderInfo.Source == consts.OrderSourceInner {
		mqProducer := l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Topics.OrderSettlementTopic)

		m := MqttMessageAckOrder{
			OrderNo: orderInfo.OrderNo,
		}

		body, err := jsonx.MarshalToString(&m)
		if err != nil {
			logx.Errorf("MqttMessageAckOrder 序列化失败err:%v", err)
			return err
		}

		msg := mq_http_sdk.PublishMessageRequest{
			MessageBody: body, //消息内容
		}

		// 设置延时订单结算消息时间
		msg.StartDeliverTime = time.Now().Add(time.Duration(l.svcCtx.Config.BusinessConf.AutoSettlementTime) * time.Minute).UnixMilli()

		_, err = mqProducer.PublishMessage(msg)
		if err != nil {
			logx.Errorf("生产者订单结算消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Topics.OrderSettlementTopic, err)
			_ = tx.Rollback()
			return err
		}
	}

	if err = tx.Commit(); err != nil {
		logx.Errorf("订单号：%s,提交事务失败err：%v", orderInfo.OrderNo, err)
		_ = tx.Rollback()
		return err
	}

	mqProducer := l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Topics.OrderTopic)

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: orderInfo.ID, //消息内容
	}
	_, err = mqProducer.PublishMessage(msg)
	if err != nil {
		logx.Errorf("生产者消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Topics.OrderTopic, err)
	}

	return nil
}

func (l *TradeLogic) RecodeOrder(tx *ent.Tx, orderInfo *ent.Order, duration float64) (refundMoney float64, err error) {

	//chargingAmount 充电总kw
	gunStatus, amountElec, amountService, duration, chargingAmount, err := l.CalculateAmount(orderInfo)
	if err != nil {
		logx.Errorf("订单号%s,枪%s,计算金额失败err:%v", orderInfo.OrderNo, orderInfo.GunNumber, err)
		return
	}
	// 优惠金额
	discountAmount := gunStatus.ChargedMoney - amountElec - amountService
	refundMoney = orderInfo.TotalAmount - gunStatus.ChargedMoney
	//上门送电
	if orderInfo.OrderType == consts.OrderTypeToHomeGivePower {
		refundMoney -= orderInfo.DoorServiceFee
	}
	if orderInfo.OrderType == consts.OrderTypeEmergencyRescue {
		refundMoney -= orderInfo.EmergencyFee
	}
	if discountAmount > 0 {
		refundMoney += discountAmount
	}

	logx.Infof("支付金额：%.2f,充电枪充电金额：%.2f, 计算电费：%.2f, 计算服务费：%.2f, 退款金额：%.2f", orderInfo.TotalAmount, gunStatus.ChargedMoney, amountElec, amountService, refundMoney)

	err = tx.TbOrderStatusLog.Create().
		SetID(l.svcCtx.Snowflake.Generate()).
		SetOrderID(orderInfo.ID).
		SetOrderNo(orderInfo.OrderNo).
		SetCurrentStatus(consts.OrderStatusOk).
		SetPreviousStatus(orderInfo.OrderStatus).
		SetOperatorID("mq").
		SetOperatorName("mq").
		SetOperatorType(4).
		SetOperationTime(time.Now()).
		SetMemo("用户主动结束订单").
		SetCreateTime(time.Now()).
		Exec(l.ctx)
	if err != nil {
		logx.Errorf("订单号%s,停止充电失败err:%v", orderInfo.OrderNo, err)
		return
	}
	err = tx.Order.UpdateOneID(orderInfo.ID).
		SetElectricityFee(amountElec).
		SetServiceFee(amountService).
		SetChargingDuration(int32(duration)).
		SetOrderStatus(consts.OrderStatusOk).
		SetChargingStatus(consts.ChargingStatusOk).
		SetDiscountAmount(discountAmount).
		SetEndTime(gunStatus.Timestamp).
		SetChargingDuration(int32(duration)).
		SetUpdateTime(time.Now()).
		SetChargingAmount(chargingAmount).
		SetRefundAmount(refundMoney).
		SetTotalAmount(orderInfo.TotalAmount - refundMoney).
		Exec(l.ctx)
	if err != nil {
		logx.Errorf("订单号%s,更新订单失败err:%v", orderInfo.OrderNo, err)
		return
	}
	// 上门订单或者应急救援更新小哥订单
	if orderInfo.OrderType == consts.OrderTypeToHomeGivePower || orderInfo.OrderType == consts.OrderTypeEmergencyRescue {
		err = tx.StaffOrderLog.Create().SetOrderID(orderInfo.ID).SetStaffID(orderInfo.StaffID).SetStatus(consts.ServiceStatusFinish).SetCreateTime(time.Now()).Exec(l.ctx)
		if err != nil {
			return
		}
	}

	return
}

func (l *TradeLogic) CalculateAmount(orderInfo *ent.Order) (gunState *tdengine.GunStatus, amountElec, amountService, duration, chargingAmount float64, err error) {
	var (
		pairs        []pair
		gunStatus    []*tdengine.GunStatus
		userInfoResp *types.UserInfo
	)

	err = mr.Finish(func() error {
		priceTemplateList, err := httpc.RequestService[types.PriceTemplateListResp](l.ctx, l.svcCtx.Config.EtcdServer.Endpoints,
			constants.DeviceService,
			"GET",
			fmt.Sprintf("/internal/gun/price?deviceNo=%s&gunNo=%s&userId=%s", orderInfo.DeviceNo, orderInfo.GunNumber, orderInfo.UserID), nil, nil)
		if err != nil {
			logx.Errorf("GetPriceTemplate err:%v", err)
			return err
		}

		for _, tpl := range priceTemplateList.Data {
			hm, _ := time.Parse("15:04", tpl.StartTime)
			pairs = append(pairs, pair{hm.Hour()*60 + hm.Minute(), tpl})
		}
		sort.Slice(pairs, func(i, j int) bool {
			return pairs[i].startMinutes < pairs[j].startMinutes
		})
		return nil
	}, func() error {
		gunStatus, err = l.svcCtx.TdEngine.GetGunStatuses(orderInfo.OrderNo)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				logx.Errorf("订单号%s,枪%s,枪状态查询失败err:%v", orderInfo.OrderNo, orderInfo.GunID, err)
				return err
			}
			logx.Errorf("订单号%s,枪%s,枪状态查询sql失败err:%v", orderInfo.OrderNo, orderInfo.GunID, err)
			return err
		}
		if len(gunStatus) == 0 {
			logx.Errorf("订单号%s,枪%s,枪状态查询失败err:%v", orderInfo.OrderNo, orderInfo.GunID, err)
			return errors.New("查询不到充电信息")
		}
		return nil
	}, func() error {

		userInfoResp, err = httpc.RequestService[types.UserInfo](l.ctx, l.svcCtx.Config.EtcdServer.Endpoints,
			constants.UserService,
			"GET",
			fmt.Sprintf("/v1/api/user/internel/info/%s", orderInfo.UserID), nil, nil)

		if err != nil {
			logx.Errorf("GetUerInfo err:%v", err)
			return err
		}

		return nil
	})
	if err != nil {
		return
	}

	gunState = gunStatus[len(gunStatus)-1]

	duration = gunState.Timestamp.Sub(gunStatus[0].Timestamp).Minutes()

	chargingAmount = gunStatus[len(gunStatus)-1].ChargedEnergy
	if gunState.ChargedMoney > orderInfo.TotalAmount {
		logx.Errorf("订单号充电金额大于充值金额%s,枪%s,orderInfo:%v", orderInfo.OrderNo, orderInfo.GunID, orderInfo)
		err = errors.New("充电金额超限")
		return
	}

	energy := 0.0
	for _, v := range gunStatus {
		usedEnergy := v.ChargedEnergy - energy
		//
		energy = v.ChargedEnergy
		template := l.getPriceTemplate(v.Timestamp, pairs)
		// 判断用户会员充电额度是否超限，超限则使用原价
		elecPrice := 0.00
		if *userInfoResp.VipCapacity > 0 {
			elecPrice = template.ElecPrice
			*userInfoResp.VipCapacity -= usedEnergy
		} else {
			elecPrice = template.OriginElecPrice
		}
		amountElec += usedEnergy * elecPrice
		amountService += usedEnergy * template.ServicePrice
	}
	return
}

func (l *TradeLogic) getPriceTemplate(t time.Time, pairs []pair) types.PriceTemplateList {
	var selected types.PriceTemplateList

	minutes := t.Hour()*60 + t.Minute()
	for _, p := range pairs {
		if minutes >= p.startMinutes {
			selected = p.tpl
		}
	}
	return selected
}

func (l *TradeLogic) insertTrade(mqttmsg MqttMessageAckTrade) error {
	trade := mqttmsg.Data
	gunNumber := fmt.Sprintf("%02d", trade.GunNo)
	_, err := l.svcCtx.DB.Trade.Create().
		SetTransactionID(trade.TradeID).
		SetDeviceCode(trade.PileCode).
		SetGunNumber(gunNumber).
		SetStartTime(trade.StartTime).
		SetEndTime(trade.EndTime).
		SetPeakPrice(trade.PeakPrice).
		SetPeakEnergy(trade.PeakEnergy).
		SetPeakLoss(trade.PeakLoss).
		SetPeakAmount(trade.PeakAmount).
		SetValleyPrice(trade.ValleyPrice).
		SetValleyEnergy(trade.ValleyEnergy).
		SetValleyLoss(trade.ValleyLoss).
		SetValleyAmount(trade.ValleyAmount).
		SetFlatPrice(trade.FlatPrice).
		SetFlatEnergy(trade.FlatEnergy).
		SetFlatLoss(trade.FlatLoss).
		SetFlatAmount(trade.FlatAmount).
		SetTroughPrice(trade.TroughPrice).
		SetTroughEnergy(trade.TroughEnergy).
		SetTroughLoss(trade.TroughLoss).
		SetTroughAmount(trade.TroughAmount).
		SetMeterStart(trade.MeterStart).
		SetMeterEnd(trade.MeterEnd).
		SetTotalEnergy(trade.TotalEnergy).
		SetTotalLoss(trade.TotalLoss).
		SetTotalAmount(trade.TotalAmount).
		SetTradeType(trade.TradeType).
		SetTradeTime(trade.TradeTime).
		SetStopReason(trade.StopReason).
		SetPhysicalCard(trade.PhysicalCard).
		SetCreateTime(time.Now()). // Assuming you want to explicitly set this; otherwise, it defaults to CURRENT_TIMESTAMP
		Save(context.Background())

	return err
}

func (l *TradeLogic) notifyHLHT(refundMoney float64, orderInfo *ent.Order, endTime time.Time) {

	chargeResultReq := ChargeResultReq{
		StartChargeSeq:     orderInfo.OuterOrderNo,
		StartChargeSeqStat: "4", // 已结束
		DeviceNo:           orderInfo.DeviceNo,
		GunNo:              orderInfo.GunNumber,
		StartTime:          endTime.Format(time.DateTime),
		Refund:             refundMoney,
		OrderNo:            orderInfo.OrderNo,
	}

	b, err := jsonx.Marshal(chargeResultReq)
	if err != nil {
		logx.Errorf("序列化失败err:%v", err)
		return
	}

	resp, err := httpc.RequestServiceCommonResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.HlhtService, "POST", "/internal/charge_result", nil, b)
	if err != nil {
		logx.Errorw("结束充电调用互联互通平台失败", logx.Field("err", err), logx.Field("订单号:", orderInfo.OrderNo))
		return
	}

	if resp.Code != 0 {
		logx.Errorw("结束充电调用互联互通平台失败", logx.Field("err", resp.Msg), logx.Field("订单号:", orderInfo.OrderNo))
		return
	}
}
