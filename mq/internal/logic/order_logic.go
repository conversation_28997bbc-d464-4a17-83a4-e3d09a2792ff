package logic

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"com.xiaojing.order_service/mq/internal/config"
	"com.xiaojing.order_service/mq/internal/svc"
	"context"
	"fmt"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"time"
)

type userConsumptionReq struct {
	UserId         string  `json:"userId"`
	TotalAmount    float64 `json:"totalAmount"`
	DisCountAmount float64 `json:"disCountAmount"`
}

type couponRegisterReq struct {
	PromotionID  string `json:"promotionId"`  //活动ID
	UserID       string `json:"userId"`       //用户ID
	CouponNumber int    `json:"couponNumber"` //发放数量
}

type notificationChargeOrderInfoRequest struct {
	StartChargeSeq   string  `json:"StartChargeSeq"`   // 启动充电序列号
	DeviceNo         string  `json:"DeviceNo"`         // 设备编号
	GunNo            string  `json:"GunNo"`            // 枪编号
	StartTime        string  `json:"StartTime"`        // 开始时间
	EndTime          string  `json:"EndTime"`          // 结束时间
	ChargingDuration int     `json:"ChargingDuration"` // 充电时长
	TotalPower       float64 `json:"TotalPower"`       // 总充电功率
	TotalElecMoney   float64 `json:"TotalElecMoney"`   // 总电费
	TotalSeviceMoney float64 `json:"TotalSeviceMoney"` // 总服务费
	TotalMoney       float64 `json:"TotalMoney"`       // 总费用
	StopReason       int     `json:"StopReason"`       // 停止原因
	StartSoc         string  `json:"StartSoc"`
	EndSoc           string  `json:"EndSoc"`
}

type MqttMessageAckOrder struct {
	OrderNo string `json:"orderNo"`
}

type OrderLogic struct {
	c      config.Config
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

func NewOrderLogic(c config.Config, svcCtx *svc.ServiceContext) *OrderLogic {
	return &OrderLogic{
		c:      c,
		svcCtx: svcCtx,
	}
}

func (l OrderLogic) Handle(data string) error {
	l.ctx = context.Background()
	orderInfo, err := l.svcCtx.DB.Order.Query().Where(order.OrderNo(data)).First(l.ctx)
	if err != nil {
		logx.Errorf("订单号：%s,查询失败不存在err：%v", data, err)
		return err
	}

	//设置充电枪空闲状态
	b, err := jsonx.Marshal(gunStatusReq{
		DeviceNo: orderInfo.DeviceNo,
		GunNo:    orderInfo.GunNumber,
		Status:   0,
	})
	if err != nil {
		logx.Errorf("订单号：%s,序列化失败err：%v", data, err)
		return err
	}

	err = httpc.RequestServiceNoResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.DeviceService, "POST", "/internal/gun/status", nil, b)
	if err != nil {
		logx.Errorf("订单号：%s,设置充电枪空闲状态失败err：%v", data, err)
		return err
	}

	// 更新订单soc,
	startSoc, endSoc, err := l.svcCtx.TdEngine.GetOrderSOC(orderInfo.OrderNo)
	if err != nil {
		logx.Errorf("订单号：%s,获取开始结束soc失败err：%v", data, err)
		return err
	}

	if orderInfo.Source == consts.OrderSourceOperate {
		// 通知互联互通平台订单信息
		threading.GoSafe(func() {
			l.notifyHLHT(orderInfo, startSoc, endSoc)
		})
		// 外部订单直接返回
		return nil
	}

	// 通知用户服务消费金额，优惠金额
	err = l.updateUserConsume(orderInfo.TotalAmount, orderInfo.DiscountAmount, orderInfo.UserID)
	if err != nil {
		logx.Errorf("订单号：%s,通知用户服务消费金额失败err：%v", data, err)
		return err
	}

	// 首单发券
	err = l.firstOrderSendCoupon(orderInfo.UserID)
	if err != nil {
		logx.Errorf("订单号：%s,首单发券失败err：%v", data, err)
		return err
	}

	err = l.svcCtx.DB.Order.UpdateOneID(orderInfo.ID).SetStartSoc(fmt.Sprintf("%d", startSoc)).SetEndSoc(fmt.Sprintf("%d", endSoc)).Exec(l.ctx)
	if err != nil {
		logx.Errorf("订单号：%s,更新开始结束soc失败err：%v", data, err)
		return err
	}

	return nil
}

func (l OrderLogic) updateUserConsume(totalAmount, discountAmount float64, userId string) error {
	req := userConsumptionReq{
		UserId:         userId,
		TotalAmount:    totalAmount,
		DisCountAmount: discountAmount,
	}

	b, err := jsonx.Marshal(req)
	if err != nil {
		return err
	}
	resp, err := httpc.RequestServiceCommonResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.UserService, "PUT", "/internal/user/consumption", nil, b)
	if err != nil {
		return err
	}

	if resp.Code != 0 {
		return fmt.Errorf(resp.Msg)
	}

	return nil
}

// 首单发券
func (l OrderLogic) firstOrderSendCoupon(userId string) error {
	count, err := l.svcCtx.DB.Order.Query().Where(order.UserID(userId)).Count(l.ctx)
	if err != nil {
		return err
	}

	// 非首单直接返回
	if count > 1 {
		return nil
	}

	// 通知优惠券服务发券
	orderFirstCoupon, err := l.svcCtx.DB.TbOrderFirstCoupon.Query().First(l.ctx)
	if err != nil {
		return err
	}

	req := couponRegisterReq{
		PromotionID:  orderFirstCoupon.PromotionID,
		UserID:       userId,
		CouponNumber: 1,
	}

	b, err := jsonx.Marshal(req)
	if err != nil {
		return err
	}
	resp, err := httpc.RequestServiceCommonResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.MarketService, "POST", "/promotions/publish_coupon", nil, b)
	if err != nil {
		return err
	}

	if resp.Code != 0 {
		return fmt.Errorf(resp.Msg)
	}

	return nil
}

func (l OrderLogic) notifyHLHT(orderInfo *ent.Order, startSoc, endSoc int) {
	req := notificationChargeOrderInfoRequest{
		StartChargeSeq:   orderInfo.OuterOrderNo,
		DeviceNo:         orderInfo.DeviceNo,
		GunNo:            orderInfo.GunNumber,
		StartTime:        orderInfo.StartTime.Format(time.DateTime),
		EndTime:          orderInfo.EndTime.Format(time.DateTime),
		ChargingDuration: int(orderInfo.ChargingDuration),
		TotalPower:       orderInfo.ChargingAmount,
		TotalElecMoney:   orderInfo.ElectricityFee,
		TotalSeviceMoney: orderInfo.ServiceFee,
		TotalMoney:       orderInfo.TotalAmount,
		StopReason:       0,
		StartSoc:         fmt.Sprintf("%d", startSoc),
		EndSoc:           fmt.Sprintf("%d", endSoc),
	}

	b, err := jsonx.MarshalToString(req)
	if err != nil {
		logx.Errorf("序列化失败err:%v", err)
		return
	}

	mqProducer := l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Topics.HlhtOrderTopic)

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: b, //消息内容
	}
	_, err = mqProducer.PublishMessage(msg)
	if err != nil {
		logx.Errorf("生产者消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Topics.HlhtOrderTopic, err)
	}

}
