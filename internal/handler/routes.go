// Code generated by goctl. DO NOT EDIT.
// goctls v1.10.10

package handler

import (
	"net/http"

	order "com.xiaojing.order_service/internal/handler/order"
	staff "com.xiaojing.order_service/internal/handler/staff"
	"com.xiaojing.order_service/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/staff/order/list",
				Handler: order.GetManageOrderListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/list",
				Handler: order.GetAdminOrderListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/admin"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/order/start",
				Handler: order.StartChargingHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/internel/list",
				Handler: order.GetOrderInternelListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/totalamount",
				Handler: order.GetAmountByIdsHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/operate/push",
				Handler: order.PushOrderOperateHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/order/create",
				Handler: order.CreateOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/create/qr",
				Handler: order.CreateQrOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/update",
				Handler: order.UpdateOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/payNotice",
				Handler: order.PayNoticeOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/order/delete",
				Handler: order.DeleteOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/list",
				Handler: order.GetOrderListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/charge/list",
				Handler: order.GetChargeOrderListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/door/list",
				Handler: order.GetDoorOrderListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/appointment/list",
				Handler: order.GetAppointmentOrderListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/detail/:orderId",
				Handler: order.GetOrderByIdHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/cancel/:orderId",
				Handler: order.CancelOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/stop",
				Handler: order.StopChargeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/rate/:orderId",
				Handler: order.RateOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/near/price",
				Handler: order.GetStationNearPriceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/pay",
				Handler: order.OrderPayHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/close",
				Handler: order.CloseOrderHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.StaffAuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/order/list",
					Handler: staff.GetStaffOrderListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/home/<USER>",
					Handler: staff.GetStaffHomeOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/order/appointTime",
					Handler: staff.GetStaffOrderAppointTimeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/order/status",
					Handler: staff.HandleOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/order/finish",
					Handler: staff.FinishOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/order/arrive",
					Handler: staff.ArriveOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/order/start",
					Handler: staff.StartOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/order/stop",
					Handler: staff.StopOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/order/detail/:orderId",
					Handler: staff.GetStaffOrderDetailHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/v2/api/staff"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/order/allocation",
				Handler: order.AllocationOrderHandler(serverCtx),
			},
		},
		rest.WithPrefix("/admin"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/order/operate",
				Handler: order.OperateOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/operate/stop",
				Handler: order.OperateOrderStopHandler(serverCtx),
			},
		},
		rest.WithPrefix("/internal"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/tb_charging_record/create",
				Handler: order.CreateTbChargingRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_charging_record/update",
				Handler: order.UpdateTbChargingRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/tb_charging_record/delete",
				Handler: order.DeleteTbChargingRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_charging_record/list",
				Handler: order.GetTbChargingRecordListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/charging-record/:recordId",
				Handler: order.GetTbChargingRecordByIdHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/device/stats/:deviceId",
				Handler: order.GetDeviceStatsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/order/complaint/submit",
				Handler: order.CreateTbOrderComplaintHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_order_complaint/update",
				Handler: order.UpdateTbOrderComplaintHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/tb_order_complaint/delete",
				Handler: order.DeleteTbOrderComplaintHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/complaint/list",
				Handler: order.GetTbOrderComplaintListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/order/complaint/detail/:complaintId",
				Handler: order.GetTbOrderComplaintByIdHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order/complaint/feedback",
				Handler: order.SubmitFeedbackHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/tb_order_status_log/create",
				Handler: order.CreateTbOrderStatusLogHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_order_status_log/update",
				Handler: order.UpdateTbOrderStatusLogHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_order_status_log/delete",
				Handler: order.DeleteTbOrderStatusLogHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_order_status_log/list",
				Handler: order.GetTbOrderStatusLogListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_order_status_log",
				Handler: order.GetTbOrderStatusLogByIdHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 小哥个人订单统计
				Method:  http.MethodGet,
				Path:    "/order/:staffId",
				Handler: staff.StaffOrdersHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v2/api/staff"),
	)
}
