package order

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"com.xiaojing.order_service/internal/logic/order"
	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"
)

// swagger:route get /v1/api/order/charge/list order GetChargeOrderList
//
// Get order list | 获取扫码充电Order信息列表
//
// Get order list | 获取扫码充电Order信息列表
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ChargeOrderListReq
//
// Responses:
//  200: OrderListResp

func GetChargeOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ChargeOrderListReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := order.NewGetChargeOrderListLogic(r.Context(), svcCtx)
		resp, err := l.GetChargeOrderList(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
