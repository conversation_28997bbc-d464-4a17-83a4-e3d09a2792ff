package order

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"context"
	"fmt"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/suyuan32/simple-admin-common/utils/pointy"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/threading"
	"time"

	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PayNoticeOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPayNoticeOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PayNoticeOrderLogic {
	return &PayNoticeOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *PayNoticeOrderLogic) PayNoticeOrder(req *types.PayNoticeReq) (resp *types.BaseMsgResp, err error) {
	orderInfo, err := l.svcCtx.DB.Order.Query().Where(order.ID(*req.OrderId)).First(l.ctx)

	if err != nil {
		l.Logger.Errorf("订单不存在：%v", err)
		return nil, err
	}

	orderStatus := consts.OrderStatusServicing
	needNotifyDevice := false

	// 根据订单类型更新订单状态
	// todo 预约单
	switch orderInfo.OrderType {
	case consts.OrderTypeToHomeGivePower, consts.OrderTypeEmergencyRescue:
		orderStatus = consts.OrderStatusWaitService
	case consts.OrderTypeStorage, consts.OrderTypeStation:
		orderStatus = consts.OrderStatusServicing

		// 非预约单且支付成功时，需要通知设备启动充电
		if orderInfo.AppointmentTime.IsZero() && *req.PaymentStatus == 1 {
			needNotifyDevice = true
		}

	default:
		return nil, fmt.Errorf("订单类型不正确")
	}

	err = l.svcCtx.DB.Order.Update().
		SetNotNilPaymentTime(pointy.GetTimeMilliPointer(req.PaymentTime)).
		SetOrderStatus(orderStatus).
		SetTransactionID(*req.TransactionId).
		SetPaymentStatus(*req.PaymentStatus).
		SetTotalAmount(*req.Amount).
		Where(order.IDEQ(*req.OrderId)).
		Exec(l.ctx)
	if err != nil {
		l.Logger.Errorf("更新订单支付信息失败：%v", err)
		return nil, err
	}

	// 非预约单，通知设备服务启动充电
	if needNotifyDevice {
		l.Logger.Infof("非预约单，通知设备启动充电 order:%v", *orderInfo)
		err = l.notifyDevice(orderInfo)
		if err != nil {
			l.Logger.Errorf("通知设备启动充电失败：%v", err)
			return &types.BaseMsgResp{
				Code: -1,
				Msg:  "通知设备启动充电失败",
			}, nil
		}
	}

	return &types.BaseMsgResp{
		Code: 0,
		Msg:  "success",
	}, nil
}

func (l *PayNoticeOrderLogic) notifyDevice(orderInfo *ent.Order) error {
	status, err := l.svcCtx.RedisClient.HGet(l.ctx, fmt.Sprintf(constants.DeviceDisconnectStatus, orderInfo.DeviceNo), orderInfo.GunNumber).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		l.Logger.Errorf("设备号：%s,枪号：%s, 在线状态获取失败err:%v", orderInfo.DeviceNo, orderInfo.GunNumber, err)
		return err
	}
	if errors.Is(err, redis.Nil) || status != "1" {
		l.Logger.Errorf("设备号：%s,枪号：%s,离线，开始订单失败", orderInfo.DeviceNo, orderInfo.GunNumber)
		return fmt.Errorf("设备离线，请确认设备是否在线")
	}

	err = l.svcCtx.DB.Order.UpdateOneID(orderInfo.ID).
		SetChargingStatus(consts.ChargingStatusStarting).
		Exec(l.ctx)
	if err != nil {
		return err
	}

	threading.GoSafe(func() {
		startChargeReq := types.StartChargeReq{
			TradeNo:    orderInfo.OrderNo,
			DeviceCode: &orderInfo.DeviceNo,
			GunNumber:  &orderInfo.GunNumber,
			Balance:    fmt.Sprintf("%.2f", orderInfo.TotalAmount),
		}
		b, err := jsonx.Marshal(startChargeReq)
		if err != nil {
			l.Logger.Errorf("序列化失败err:%v", err)
			return
		}

		startResp, err := httpc.RequestServiceCommonResp(context.Background(), l.svcCtx.Config.EtcdServer.Endpoints, constants.DeviceService, "POST", "/command/startCharge", map[string]string{"Content-Type": "application/json"}, b)
		if err != nil {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderInfo.OrderNo, err)
		}
		if startResp.Code != 0 {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderInfo.OrderNo, startResp.Msg)
		}
	})

	// 加入延迟队列，一分钟后订单充电状态仍为启动充电中，则结束订单并退款
	m := struct {
		OrderId string `json:"orderId"`
	}{
		OrderId: orderInfo.OrderNo,
	}

	body, err := jsonx.MarshalToString(&m)
	if err != nil {
		l.Logger.Errorf("Marshal err:%v", err)
		return err
	}

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: body, //消息内容
	}

	msg.StartDeliverTime = time.Now().Add(time.Duration(1) * time.Minute).UnixMilli()

	_, err = l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback).PublishMessage(msg)
	if err != nil {
		l.Logger.Errorf("生产者消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback, err)
		return err
	}

	return nil
}
