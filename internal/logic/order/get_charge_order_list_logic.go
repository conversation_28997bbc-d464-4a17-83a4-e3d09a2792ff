package order

import (
	"context"

	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetChargeOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetChargeOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetChargeOrderListLogic {
	return &GetChargeOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetChargeOrderListLogic) GetChargeOrderList(req *types.ChargeOrderListReq) (resp *types.OrderListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
