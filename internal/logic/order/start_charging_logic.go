package order

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"com.xiaojing.order_service/internal/utils/dberrorhandler"
	"context"
	"fmt"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/suyuan32/simple-admin-common/i18n"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/threading"
	"time"

	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type StartChargingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewStartChargingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StartChargingLogic {
	return &StartChargingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// StartCharging 到场扫码开始充电
func (l *StartChargingLogic) StartCharging(req *types.StartChargingOrderIdReq) (resp *types.BaseMsgResp, err error) {
	// todo 预约单逻辑
	orderInfo, err := l.svcCtx.DB.Order.Query().Where(order.ID(*req.OrderId)).First(l.ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(l.Logger, err, req)
	}
	if orderInfo.OrderStatus != consts.OrderStatusServicing || orderInfo.ChargingStatus != consts.ChargingStatusNoStart || orderInfo.PaymentStatus != consts.PayStatusOk {
		l.Logger.Errorf("订单号%s,枪%s,订单状态错误order:%v", orderInfo.OrderNo, orderInfo.GunNumber, *orderInfo)
		return &types.BaseMsgResp{
			Code: -1,
			Msg:  "该订单状态无法开始充电",
		}, nil
	}

	status, err := l.svcCtx.RedisClient.HGet(l.ctx, fmt.Sprintf(constants.DeviceDisconnectStatus, orderInfo.DeviceNo), orderInfo.GunNumber).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		l.Logger.Errorf("设备号：%s,枪号：%s, 在线状态获取失败err:%v", orderInfo.DeviceNo, orderInfo.GunNumber, err)
		return nil, err
	}
	if errors.Is(err, redis.Nil) || status != "1" {
		l.Logger.Errorf("设备号：%s,枪号：%s,离线，开始订单失败", orderInfo.DeviceNo, orderInfo.GunNumber)
		return &types.BaseMsgResp{
			Code: -1,
			Msg:  "设备离线，请确认设备是否在线",
		}, nil
	}

	err = l.svcCtx.DB.Order.UpdateOneID(orderInfo.ID).
		SetChargingStatus(consts.ChargingStatusStarting).
		Exec(l.ctx)
	if err != nil {
		return nil, err
	}

	threading.GoSafe(func() {
		startChargeReq := types.StartChargeReq{
			TradeNo:    orderInfo.OrderNo,
			DeviceCode: &orderInfo.DeviceNo,
			GunNumber:  &orderInfo.GunNumber,
			Balance:    fmt.Sprintf("%.2f", orderInfo.TotalAmount),
		}
		b, err := jsonx.Marshal(startChargeReq)
		if err != nil {
			l.Logger.Errorf("序列化失败err:%v", err)
			return
		}

		startResp, err := httpc.RequestServiceCommonResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.DeviceService, "POST", "/command/startCharge", map[string]string{"Content-Type": "application/json"}, b)
		if err != nil {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderInfo.OrderNo, err)
		}
		if startResp.Code != 0 {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderInfo.OrderNo, startResp.Msg)
		}
	})

	m := struct {
		OrderId string `json:"orderId"`
	}{
		OrderId: *req.OrderId,
	}

	body, err := jsonx.MarshalToString(&m)
	if err != nil {
		l.Logger.Errorf("Marshal err:%v", err)
		return nil, err
	}

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: body, //消息内容
	}

	msg.StartDeliverTime = time.Now().Add(time.Duration(1) * time.Minute).UnixMilli()

	_, err = l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback).PublishMessage(msg)
	if err != nil {
		l.Logger.Errorf("生产者消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback, err)
		return nil, err
	}

	return &types.BaseMsgResp{
		Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
	}, nil

}
