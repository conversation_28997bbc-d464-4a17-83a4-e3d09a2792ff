package order

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/middleware"
	"com.xiaojing.order_service/ent"
	"context"

	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/ent/predicate"
	"com.xiaojing.order_service/internal/common"
	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"
	"com.xiaojing.order_service/internal/utils/dberrorhandler"

	"github.com/suyuan32/simple-admin-common/i18n"

	"github.com/suyuan32/simple-admin-common/utils/pointy"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrderListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrderListLogic {
	return &GetOrderListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetOrderListLogic) GetOrderList(req *types.OrderListReq) (*types.OrderListResp, error) {
	var predicates []predicate.Order

	userId := middleware.GetUidFromCtx(l.ctx)
	if userId == "" {
		return &types.OrderListResp{
			BaseDataInfo: types.BaseDataInfo{
				Code: -1,
				Msg:  "未登录",
			},
		}, nil
	}
	predicates = append(predicates, order.UserIDContains(userId))
	predicates = append(predicates, order.PaymentStatusIn(2, 1))

	if req.OrderType != nil {
		predicates = append(predicates, order.OrderTypeEQ(*req.OrderType))
	}
	if req.OrderStatus != nil {
		predicates = append(predicates, order.OrderStatusEQ(*req.OrderStatus))
	}
	if req.StartTime != nil {
		predicates = append(predicates, order.CreateTimeGTE(common.DateStringToInt64Ptr(*req.StartTime)))
	}
	if req.EndTime != nil {
		predicates = append(predicates, order.CreateTimeLTE(common.DateStringToInt64Ptr(*req.EndTime)))
	}

	data, err := l.svcCtx.DB.Order.Query().Where(predicates...).Order(ent.Desc(order.FieldCreateTime)).Page(l.ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(l.Logger, err, req)
	}

	resp := &types.OrderListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.PageDetails.Total

	for _, v := range data.List {
		resp.Data.Data = append(resp.Data.Data,
			types.OrderInfo{
				OrderId:          &v.OrderNo,
				OrderNo:          &v.OrderNo,
				UserId:           &v.UserID,
				DeviceId:         &v.DeviceID,
				GunId:            &v.GunID,
				StaffId:          &v.StaffID,
				VehicleId:        &v.VehicleID,
				MerchantId:       &v.MerchantID,
				StationId:        &v.StationID,
				OrderType:        &v.OrderType,
				OrderStatus:      &v.OrderStatus,
				ServiceStatus:    &v.ServiceStatus,
				ChargingStatus:   &v.ChargingStatus,
				AppointmentTime:  pointy.GetUnixMilliPointer(v.AppointmentTime.UnixMilli()),
				StartTime:        pointy.GetUnixMilliPointer(v.StartTime.UnixMilli()),
				EndTime:          pointy.GetUnixMilliPointer(v.EndTime.UnixMilli()),
				ServiceAddress:   &v.ServiceAddress,
				Longitude:        &v.Longitude,
				Latitude:         &v.Latitude,
				ChargingAmount:   &v.ChargingAmount,
				ChargingDuration: &v.ChargingDuration,
				ElectricityFee:   &v.ElectricityFee,
				ServiceFee:       &v.ServiceFee,
				TimeFee:          &v.TimeFee,
				DiscountAmount:   &v.DiscountAmount,
				TotalAmount:      &v.TotalAmount,
				PaymentStatus:    &v.PaymentStatus,
				PaymentTime:      pointy.GetUnixMilliPointer(v.PaymentTime.UnixMilli()),
				PaymentMethod:    &v.PaymentMethod,
				TransactionId:    &v.TransactionID,
				CancelReason:     &v.CancelReason,
				CancelTime:       pointy.GetUnixMilliPointer(v.CancelTime.UnixMilli()),
				RefundTime:       pointy.GetUnixMilliPointer(v.RefundTime.UnixMilli()),
				RefundAmount:     &v.RefundAmount,
				Rating:           &v.Rating,
				Comment:          &v.Comment,
				CommentTime:      pointy.GetUnixMilliPointer(v.CommentTime.UnixMilli()),
				MerchantIncome:   &v.MerchantIncome,
				PlatformIncome:   &v.PlatformIncome,
				SettlementStatus: &v.SettlementStatus,
				SettlementTime:   pointy.GetUnixMilliPointer(v.SettlementTime.UnixMilli()),
				InvoiceStatus:    &v.InvoiceStatus,
				StartSoc:         &v.StartSoc,
				EndSoc:           &v.EndSoc,
				Memo:             &v.Memo,
				CreateTime:       pointy.GetPointer(v.CreateTime.UnixMilli()),
				UpdateTime:       pointy.GetPointer(v.UpdateTime.UnixMilli()),
				EstimatedElec:    pointy.GetPointer[int](v.EstimatedElec),
			})
	}

	return resp, nil
}
