package order

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/consts"
	"context"
	"fmt"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/threading"
	"time"

	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type OperateOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewOperateOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OperateOrderLogic {
	return &OperateOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *OperateOrderLogic) OperateOrder(req *types.OperateOrderReq) (resp *types.BaseMsgResp, err error) {

	// 判断外部订单号是否存在
	_, err = l.svcCtx.DB.Order.Query().Where(order.OuterOrderNo(*req.OrderNo)).First(l.ctx)
	if err == nil {
		return &types.BaseMsgResp{
			Code: -1,
			Msg:  "订单已存在",
		}, nil
	}

	orderNo := fmt.Sprintf("%s%s%d", *req.OrderNo, l.svcCtx.Snowflake.Generate(), time.Now().UnixMilli()%10000)

	err = l.notifyDevice(req, orderNo)
	if err != nil {
		l.Logger.Errorf("通知设备启动充电失败：%v", err)
		return &types.BaseMsgResp{
			Code: -1,
			Msg:  "设备离线，请确认设备是否在线",
		}, nil
	}

	_, err = l.svcCtx.DB.Order.Create().
		SetID(orderNo).
		SetNotNilOrderNo(&orderNo).
		SetNotNilOuterOrderNo(req.OrderNo).
		SetNotNilOuterPhone(req.PhoneNum).
		SetNotNilOuterUserID(req.OuterUserId).
		SetOrderType(consts.OrderTypeStation).
		SetOrderStatus(consts.OrderStatusServicing).
		SetChargingStatus(consts.ChargingStatusStarting).
		SetNotNilTotalAmount(req.Amount).
		SetPaymentStatus(consts.PayStatusOk).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(l.ctx)

	if err != nil {
		l.Logger.Errorf("创建订单失败：%v", err)
		return nil, err
	}

	return
}

func (l *OperateOrderLogic) notifyDevice(req *types.OperateOrderReq, orderNo string) error {
	status, err := l.svcCtx.RedisClient.HGet(l.ctx, fmt.Sprintf(constants.DeviceDisconnectStatus, *req.DeviceNo), *req.GunNo).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		l.Logger.Errorf("设备号：%s,枪号：%s, 在线状态获取失败err:%v", *req.DeviceNo, *req.GunNo, err)
		return err
	}
	if errors.Is(err, redis.Nil) || status != "1" {
		l.Logger.Errorf("设备号：%s,枪号：%s,离线，开始订单失败", *req.DeviceNo, *req.GunNo)
		return fmt.Errorf("设备离线，请确认设备是否在线")
	}

	if err != nil {
		return err
	}

	threading.GoSafe(func() {
		startChargeReq := types.StartChargeReq{
			TradeNo:    orderNo,
			DeviceCode: req.DeviceNo,
			GunNumber:  req.GunNo,
			Balance:    fmt.Sprintf("%.2f", *req.Amount),
		}
		b, err := jsonx.Marshal(startChargeReq)
		if err != nil {
			l.Logger.Errorf("序列化失败err:%v", err)
			return
		}

		startResp, err := httpc.RequestServiceCommonResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.DeviceService, "POST", "/command/startCharge", nil, b)
		if err != nil {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderNo, err)
			return
		}
		if startResp.Code != 0 {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderNo, startResp.Msg)
			return
		}
	})

	// 加入延迟队列，一分钟后订单充电状态仍为启动充电中，则通知互联互通服务启动失败
	m := struct {
		OrderId string `json:"orderId"`
	}{
		OrderId: orderNo,
	}

	body, err := jsonx.MarshalToString(&m)
	if err != nil {
		l.Logger.Errorf("Marshal err:%v", err)
		return err
	}

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: body, //消息内容
	}

	msg.StartDeliverTime = time.Now().Add(time.Duration(1) * time.Minute).UnixMilli()

	_, err = l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback).PublishMessage(msg)
	if err != nil {
		l.Logger.Errorf("生产者消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback, err)
		return err
	}

	return nil
}
