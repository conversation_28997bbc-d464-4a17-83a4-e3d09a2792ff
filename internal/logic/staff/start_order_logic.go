package staff

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/httpc"
	"com.xiaojing.order_service/internal/consts"
	"context"
	"errors"
	"fmt"
	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/threading"
	"strings"
	"time"

	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/middleware"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"com.xiaojing.order_service/ent/order"
	"com.xiaojing.order_service/internal/svc"
	"com.xiaojing.order_service/internal/types"
	"com.xiaojing.order_service/internal/utils/dberrorhandler"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
)

type StartOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewStartOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StartOrderLogic {
	return &StartOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *StartOrderLogic) StartOrder(req *types.StartOrderReq) (resp *types.BaseMsgResp, err error) {
	orderInfo, err := l.svcCtx.DB.Order.Query().
		Where(order.ID(*req.OrderId)).
		Where(order.StaffID(middleware.GetStaffIdFromCtx(l.ctx))).
		First(l.ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(l.Logger, err, req)
	}
	if orderInfo.OrderStatus != consts.OrderStatusWaitService || orderInfo.ServiceStatus != consts.ServiceStatusAccept || orderInfo.ChargingStatus != consts.ChargingStatusNoStart {
		return &types.BaseMsgResp{
			Code: -1,
			Msg:  "非待服务订单，无法到达",
		}, nil
	}

	status, err := l.svcCtx.RedisClient.HGet(l.ctx, fmt.Sprintf(constants.DeviceDisconnectStatus, orderInfo.DeviceNo), orderInfo.GunNumber).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		l.Logger.Errorf("设备号：%s,枪号：%s, 在线状态获取失败err:%v", orderInfo.DeviceNo, orderInfo.GunNumber, err)
		return nil, err
	}
	if errors.Is(err, redis.Nil) || status != "1" {
		l.Logger.Errorf("设备号：%s,枪号：%s,离线，开始订单失败", orderInfo.DeviceNo, orderInfo.GunNumber)
		return &types.BaseMsgResp{
			Code: -1,
			Msg:  "设备离线，请确认设备是否在线",
		}, nil
	}

	tx, err := l.svcCtx.DB.Tx(l.ctx)
	if err != nil {
		return nil, err
	}
	err = tx.Order.UpdateOneID(orderInfo.ID).
		SetOrderStatus(consts.OrderStatusServicing).
		SetServiceStatus(consts.ServiceStatusServicing).
		SetChargingStatus(consts.ChargingStatusStarting).
		Exec(l.ctx)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}
	err = tx.StaffOrderLog.Create().SetOrderID(orderInfo.ID).SetStaffID(middleware.GetStaffIdFromCtx(l.ctx)).SetStatus(consts.ServiceStatusServicing).SetCreateTime(time.Now()).Exec(l.ctx)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}

	//开始前图片
	err = tx.OrderImage.Create().SetOrderID(orderInfo.ID).SetStatus(1).SetImages(strings.Join(req.Images, ",")).SetCreateTime(time.Now()).Exec(l.ctx)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}

	threading.GoSafe(func() {
		startChargeReq := types.StartChargeReq{
			TradeNo:    orderInfo.OrderNo,
			DeviceCode: &orderInfo.DeviceNo,
			GunNumber:  &orderInfo.GunNumber,
			Balance:    fmt.Sprintf("%.2f", orderInfo.TotalAmount),
		}
		b, err := jsonx.Marshal(startChargeReq)
		if err != nil {
			l.Logger.Errorf("序列化失败err:%v", err)
			return
		}

		startResp, err := httpc.RequestServiceCommonResp(l.ctx, l.svcCtx.Config.EtcdServer.Endpoints, constants.DeviceService, "POST", "/command/startCharge", map[string]string{"Content-Type": "application/json"}, b)
		if err != nil {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderInfo.OrderNo, err)
		}
		if startResp.Code != 0 {
			l.Logger.Errorf("开始充电订单号：%s, 失败err:%v", orderInfo.OrderNo, startResp.Msg)
		}
	})

	m := struct {
		OrderId string `json:"orderId"`
	}{
		OrderId: *req.OrderId,
	}

	body, err := jsonx.MarshalToString(&m)
	if err != nil {
		l.Logger.Errorf("Marshal err:%v", err)
		return nil, err
	}

	msg := mq_http_sdk.PublishMessageRequest{
		MessageBody: body, //消息内容
	}

	msg.StartDeliverTime = time.Now().Add(time.Duration(1) * time.Minute).UnixMilli()

	_, err = l.svcCtx.RocketMqClient.GetProducer(l.svcCtx.Config.RocketConsumerConf.InstanceId, l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback).PublishMessage(msg)
	if err != nil {
		l.Logger.Errorf("生产者消息topic:%s, 发送失败：err:%v", l.svcCtx.Config.RocketConsumerConf.Groups.OrderRollback, err)
		return nil, err
	}

	if err = tx.Commit(); err != nil {
		return nil, err
	}
	return &types.BaseMsgResp{
		Code: 0,
		Msg:  "操作成功",
	}, nil
}
