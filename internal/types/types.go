// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// swagger:model BaseCommonDataInfo
type BaseCommonDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data interface{} `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `form:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `form:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
// swagger:model IDInt32Req
type IDInt32Req struct {
	// ID
	// Required: true
	Id int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
// swagger:model IDsInt32Req
type IDsInt32Req struct {
	// IDs
	// Required: true
	Ids []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
// swagger:model IDInt32PathReq
type IDInt32PathReq struct {
	// ID
	// Required: true
	Id int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
// swagger:model IDUint32Req
type IDUint32Req struct {
	// ID
	// Required: true
	Id uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
// swagger:model IDsUint32Req
type IDsUint32Req struct {
	// IDs
	// Required: true
	Ids []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
// swagger:model IDUint32PathReq
type IDUint32PathReq struct {
	// ID
	// Required: true
	Id uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
// swagger:model IDInt64Req
type IDInt64Req struct {
	// ID
	// Required: true
	Id int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
// swagger:model IDsInt64Req
type IDsInt64Req struct {
	// IDs
	// Required: true
	Ids []int64 `json:"ids"`
}

// Basic ID request (int64) | 基础ID地址参数请求 (int64)
// swagger:model IDInt64PathReq
type IDInt64PathReq struct {
	// ID
	// Required: true
	Id int64 `path:"id"`
}

// Basic ID request (string) | 基础ID参数请求 (string)
// swagger:model IDStringReq
type IDStringReq struct {
	// ID
	// Required: true
	Id string `json:"id"`
}

// Basic IDs request (string) | 基础ID数组参数请求 (string)
// swagger:model IDsStringReq
type IDsStringReq struct {
	// IDs
	// Required: true
	Ids []string `json:"ids"`
}

// Basic ID request (string) | 基础ID地址参数请求 (string)
// swagger:model IDStringPathReq
type IDStringPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:model UUIDPathReq
type UUIDPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
// swagger:model BaseIDInt64Info
type BaseIDInt64Info struct {
	// ID
	Id *int64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
// swagger:model BaseIDInt32Info
type BaseIDInt32Info struct {
	// ID
	Id *int32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
// swagger:model BaseIDUint32Info
type BaseIDUint32Info struct {
	// ID
	Id *uint32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (string) | 基础ID信息 (string)
// swagger:model BaseIDStringInfo
type BaseIDStringInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The data of manage order information | ManageOrder信息
// swagger:model ManageOrderInfo
type ManageOrderInfo struct {
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 订单类型：1-预约充电，2-上门充电，3-应急救援
	OrderType *int8 `json:"orderType,optional"`
	// 服务状态：0-待分配，1-已分配，2-已接单  3-已拒单 4-前往中，5-服务中，6-已完成
	ServiceStatus *int8 `json:"serviceStatus,optional"`
	// 服务地址
	ServiceAddress *string `json:"serviceAddress,optional"`
	// 充电电量(kWh)
	ChargingAmount *float64 `json:"chargingAmount,optional"`
	// 支付状态：0-未支付，1-已支付，2-已退款
	PaymentStatus *int8 `json:"paymentStatus,optional"`
	// 联系人
	ContactName *string `json:"contactName,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of manage order list | ManageOrder信息列表数据
// swagger:model ManageOrderListResp
type ManageOrderListResp struct {
	BaseDataInfo
	// The manage order list data | ManageOrder信息列表数据
	Data ManageOrderListInfo `json:"data"`
}

// The manage order list data | ManageOrder信息列表数据
// swagger:model ManageOrderListInfo
type ManageOrderListInfo struct {
	BaseListInfo
	// The manage order list data | ManageOrder信息列表数据
	Data []ManageOrderInfo `json:"data"`
}

// Get manage order list request params | ManageOrder信息列表请求参数
// swagger:model AdminStaffOrderListReq
type AdminStaffOrderListReq struct {
	PageInfo
	// 订单编号
	OrderNo *string `form:"orderNo,optional"`
	// 客户名称
	ContactName *string `form:"contactName,optional"`
	// 客户地址
	ServiceAddress *string `form:"serviceAddress,optional"`
	// 订单类型：1-预约充电，2-上门充电，3-应急救援
	OrderType *int8 `form:"orderType,optional"`
	// 服务状态：0-待分配，1-已分配，2-已接单  3-已拒单 4-前往中，5-服务中，6-已完成
	ServiceStatus *int8 `form:"serviceStatus,optional"`
	// 开始时间
	StartTime *string `form:"startTime,optional"`
	// 结束时间
	EndTime *string `form:"endTime,optional"`
}

// swagger:model AdminOrderListReq
type AdminOrderListReq struct {
	PageInfo
	// 订单编号
	OrderNo *string `form:"orderNo,optional"`
	// 用户ID
	UserId *string `form:"userId,optional"`
	// 用户手机
	Phone *string `form:"phone,optional"`
	// 订单状态 0-待支付，1-支付中，2-待服务，3-服务中，4-已完成，5-已取消，6-已退款
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 开始时间
	StartTime *string `form:"startTime,optional"`
	// 结束时间
	EndTime *string `form:"endTime,optional"`
}

// swagger:model VehicleModelInfo
type VehicleModelInfo struct {
	// 车型ID，主键
	ModelId *string `json:"modelId,optional"`
	// 品牌ID
	BrandId   *string `json:"brandId,optional"`
	BrandName *string `json:"brandName,optional"`
	// 车型名称
	ModelName *string `json:"modelName,optional"`
	// 年款
	ModelYear *string `json:"modelYear,optional"`
	// 电池容量(kWh)
	BatteryCapacity *float64 `json:"batteryCapacity,optional"`
	// 快充功率(kW)
	FastChargingPower *float64 `json:"fastChargingPower,optional"`
	// 慢充功率(kW)
	SlowChargingPower *float64 `json:"slowChargingPower,optional"`
	// 续航里程(km)
	EnduranceMileage *int64 `json:"enduranceMileage,optional"`
	// 电池类型
	BatteryType *string `json:"batteryType,optional"`
	// 车型图片URL
	ModelImage *string `json:"modelImage,optional"`
	// 价格区间
	PriceRange *string `json:"priceRange,optional"`
	// 车型描述
	Description *string `json:"description,optional"`
	// 状态：0-禁用，1-启用
	Status *int8 `json:"status,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// swagger:model TradeInfo
type TradeInfo struct {
	TradeID      string  // 交易流水号
	PileCode     string  // 桩编号
	GunNo        string  // 枪号
	StartTime    string  // 开始时间
	EndTime      string  // 结束时间
	PeakPrice    float64 // 尖单价
	PeakEnergy   float64 // 尖电量
	PeakLoss     float64 // 计损尖电量
	PeakAmount   float64 // 尖金额
	ValleyPrice  float64 // 峰单价
	ValleyEnergy float64 // 峰电量
	ValleyLoss   float64 // 计损峰电量
	ValleyAmount float64 // 峰金额
	FlatPrice    float64 // 平单价
	FlatEnergy   float64 // 平电量
	FlatLoss     float64 // 计损平电量
	FlatAmount   float64 // 平金额
	TroughPrice  float64 // 谷单价
	TroughEnergy float64 // 谷电量
	TroughLoss   float64 // 计损谷电量
	TroughAmount float64 // 谷金额
	MeterStart   float64 // 电表总起值
	MeterEnd     float64 // 电表总止值
	TotalEnergy  float64 // 总电量
	TotalLoss    float64 // 计损总电量
	TotalAmount  float64 // 消费金额
	VIN          string  // VIN码
	TradeType    int     // 交易标识  1.app启动 2.卡启动 3.离线卡启动 4.vin码启动
	TradeTime    string  // 交易日期、时间
	StopReason   int     // 停止原因 没有说明
	PhysicalCard string  // 物理卡号
}

type PriceTemplateList struct {
	TemplateListId  string  `json:"templateListId,optional"`
	StartTime       string  `json:"startTime"`
	PriceType       int64   `json:"priceType"`
	ElecPrice       float64 `json:"elecPrice"`
	ServicePrice    float64 `json:"servicePrice"`
	EmergencyPrice  float64 `json:"emergency_price,default=0.00"`
	DoorPrice       float64 `json:"doorPrice,default=0.00"`
	OriginElecPrice float64 `json:"originElecPrice,default=0.00"`
	AppointOccupy   float64 `json:"appointOccupy,default=0.00"`
	TimeoutOccupy   float64 `json:"timeoutOccupy,default=0.00"`
	FreeOccupy      int     `json:"freeOccupy,default=0"`
}

// swagger:model PriceTemplateListResp
type PriceTemplateListResp struct {
	Data []PriceTemplateList `json:"data"`
}

// swagger:model UserInfoResp
type UserInfoResp struct {
	BaseDataInfo
	Data UserInfo `json:"data"`
}

// swagger:model UserInfo
type UserInfo struct {
	UserId           *string  `json:"userId"`
	Avatar           *string  `json:"avatar"`
	Name             *string  `json:"name"`
	Phone            *string  `json:"phone"`
	Level            *int8    `json:"level"`
	Points           *int32   `json:"points"`
	Gender           *int8    `json:"gender"`
	RegisterTime     *int64   `json:"registerTime"`
	LastLoginTime    *int64   `json:"lastLoginTime"`
	Balance          *float64 `json:"balance"`
	GiveBalance      *float64 `json:"giveBalance"`
	EmergencyContact *string  `json:"emergencyContact"`
	Memo             *string  `json:"memo"`
	Status           *int8    `json:"status"`
	CreateTime       *int64   `json:"createTime"`
	Consumption      *float64 `json:"consumption"`
	VipCapacity      *float64 `json:"vipCapacity"`
	SaveMoney        *float64 `json:"save_money"`
	VipExpireDate    *string  `json:"vipExpireDate"`
	LevelName        *string  `json:"levelName"`
	Discount         *float64 `json:"discount"`
	WxOpenId         *string  `json:"wxOpenId"`
}

// swagger:model AutoRefundReq
type AutoRefundReq struct {
	OrderId      string `json:"orderId"`
	Reason       string `json:"reason"`
	RefundAmount int64  `json:"refundAmount"`
}

// swagger:model RefundInfo
type RefundInfo struct {
	RefundId     string `json:"refundId"`
	RefundNo     string `json:"refundNo"`
	RefundAmount int64  `json:"refundAmount"`
	Status       int8   `json:"status"`
}

// swagger:model StartChargeReq
type StartChargeReq struct {
	TradeNo    string  `json:"tradeNo"`
	DeviceCode *string `json:"deviceCode,optional"`
	GunNumber  *string `json:"gunNumber,optional"`
	Balance    string  `json:"balance"`
	DeviceId   *string `json:"deviceId,optional"`
	GunId      *string `json:"gunId,optional"`
}

// swagger:model StopChargeReq
type StopChargeReq struct {
	DeviceCode *string `json:"deviceCode,optional"`
	DeviceId   *string `json:"deviceId,optional"`
	GunNumber  *string `json:"gunNumber,optional"`
	GunId      *string `json:"gunId,optional"`
}

// swagger:model DeviceStatusReq
type DeviceStatusReq struct {
	DeviceId []string `json:"deviceId"`
}

// swagger:model DeviceStatusResp
type DeviceStatusResp struct {
	List []DeviceStatus `json:"list"`
}

type DeviceStatus struct {
	DeviceId string `json:"deviceId"`
	Status   int8   `json:"status"`
}

// swagger:model StationAreaPriceReq
type StationAreaPriceReq struct {
	Lat   *float64 `json:"lat"`
	Lon   *float64 `json:"lon"`
	Types *int8    `json:"types"`
}

// swagger:model StationAreaPriceResp
type StationAreaPriceResp struct {
	StationId   string                 `json:"stationId"`
	StationName string                 `json:"stationName"`
	TemplateId  string                 `json:"templateId"`
	Distance    float64                `json:"distance"`
	List        []StationAreaPriceInfo `json:"list"`
}

// swagger:model StationAreaPriceInfo
type StationAreaPriceInfo struct {
	StartTime      string  `json:"startTime"`
	ElecPrice      float64 `json:"ElecPrice"`
	ServicePrice   float64 `json:"servicePrice"`
	DoorPrice      float64 `json:"doorPrice,optional"`
	EmergencyPrice float64 `json:"emergencyPrice,optional"`
}

// The data of order information | Order信息
// swagger:model OrderInfo
type OrderInfo struct {
	// 订单ID，主键
	OrderId *string `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 设备编号
	DeviceNo *string `json:"deviceNo,optional"`
	// 充电枪ID
	GunId *string `json:"gunId,optional"`
	// 充电枪编号
	GunNo *string `json:"gunNo,optional"`
	// 服务人员ID
	StaffId *string `json:"staffId,optional"`
	// 车辆ID
	VehicleId *string `json:"vehicleId,optional"`
	// 商户ID
	MerchantId *string `json:"merchantId,optional"`
	// 充电站ID
	StationId *string `json:"stationId,optional"`
	// 订单类型：1-固定桩充电，2-上门充电，3-储能柜充电
	OrderType *int8 `json:"orderType,optional"`
	// 订单状态：0-待支付，1-支付中，2-待服务，3-服务中，4-已完成，5-已取消，6-已退款
	OrderStatus *int8 `json:"orderStatus,optional"`
	// 服务状态：0-待分配，1-已分配，2-前往中，3-服务中，4-已完成
	ServiceStatus *int8 `json:"serviceStatus,optional"`
	// 充电状态：0-未开始，1-充电中，2-已完成，3-异常中断
	ChargingStatus *int8 `json:"chargingStatus,optional"`
	// 预约时间
	AppointmentTime *int64 `json:"appointmentTime,optional"`
	// 实际开始时间
	StartTime *int64 `json:"startTime,optional"`
	// 实际结束时间
	EndTime *int64 `json:"endTime,optional"`
	// 服务地址
	ServiceAddress *string `json:"serviceAddress,optional"`
	// 服务位置经度
	Longitude *float64 `json:"longitude,optional"`
	// 服务位置纬度
	Latitude *float64 `json:"latitude,optional"`
	// 充电电量(kWh)
	ChargingAmount *float64 `json:"chargingAmount,optional"`
	// 充电时长(分钟)
	ChargingDuration *int32 `json:"chargingDuration,optional"`
	// 电费(元)
	ElectricityFee *float64 `json:"electricityFee,optional"`
	// 服务费(元)
	ServiceFee *float64 `json:"serviceFee,optional"`
	// 时长费(元)
	TimeFee *float64 `json:"timeFee,optional"`
	// 优惠金额(元)
	DiscountAmount *float64 `json:"discountAmount,optional"`
	// 订单总金额(元)
	TotalAmount *float64 `json:"totalAmount,optional"`
	// 支付状态：0-未支付，1-已支付，2-已退款
	PaymentStatus *int8 `json:"paymentStatus,optional"`
	// 支付时间
	PaymentTime *int64 `json:"paymentTime,optional"`
	// 支付方式：1-微信支付，2-支付宝，3-余额支付
	PaymentMethod *int8 `json:"paymentMethod,optional"`
	// 支付交易号
	TransactionId *string `json:"transactionId,optional"`
	// 取消原因
	CancelReason *string `json:"cancelReason,optional"`
	// 取消时间
	CancelTime *int64 `json:"cancelTime,optional"`
	// 退款时间
	RefundTime *int64 `json:"refundTime,optional"`
	// 退款金额(元)
	RefundAmount *float64 `json:"refundAmount,optional"`
	// 评价分数：1-5星
	Rating *int8 `json:"rating,optional"`
	// 评价内容
	Comment *string `json:"comment,optional"`
	// 评价时间
	CommentTime *int64 `json:"commentTime,optional"`
	// 商户收入(元)
	MerchantIncome *float64 `json:"merchantIncome,optional"`
	// 平台收入(元)
	PlatformIncome *float64 `json:"platformIncome,optional"`
	// 结算状态：0-未结算，1-已结算
	SettlementStatus *int8 `json:"settlementStatus,optional"`
	// 结算时间
	SettlementTime *int64 `json:"settlementTime,optional"`
	// 开票状态：0-未开票，1-已开票
	InvoiceStatus *int8 `json:"invoiceStatus,optional"`
	// 开始SOC(%)
	StartSoc *string `json:"startSoc,optional"`
	// 结束SOC(%)
	EndSoc *string `json:"endSoc,optional"`
	// 备注
	Memo                 *string  `json:"memo,optional"`
	Images               *string  `json:"images,optional"`
	EstimatedElec        *int     `json:"estimatedElec,optional"`
	ContactName          *string  `json:"contactName,optional"`
	ContactPhone         *string  `json:"contactPhone,optional"`
	Gender               *int8    `json:"gender,optional"`
	VehicleDetailAddress *string  `json:"vehicleDetailAddress,optional"`
	VehicleNo            *string  `json:"vehicleNo,optional"`
	DoorServiceFee       *float64 `json:"doorServiceFee,optional"`
	EstimatedFee         *float64 `json:"estimatedFee,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of order list | Order信息列表数据
// swagger:model OrderListResp
type OrderListResp struct {
	BaseDataInfo
	// The order list data | Order信息列表数据
	Data OrderListInfo `json:"data"`
}

// The order list data | Order信息列表数据
// swagger:model OrderListInfo
type OrderListInfo struct {
	BaseListInfo
	// The order list data | Order信息列表数据
	Data []OrderInfo `json:"data"`
}

// Get order list request params | 小哥订单信息列表请求参数
// swagger:model StaffOrderListReq
type StaffOrderListReq struct {
	PageInfo
	OrderType *int8 `form:"orderType,optional"`
	// 订单状态
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 实际开始时间
	StartTime *string `form:"startTime,optional"`
	// 实际结束时间
	EndTime *string `form:"endTime,optional"`
	// 服务状态
	ServiceStatus *int8 `form:"serviceStatus,optional"`
}

// swagger:model OrderListInternalReq
type OrderListInternalReq struct {
	PageInfo
	// 用户ID
	UserId *string `form:"userId,optional"`
	// 订单类型
	OrderType *int8 `form:"orderType,optional"`
	// 订单状态
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 实际开始时间
	StartTime *string `form:"startTime,optional"`
	// 实际结束时间
	EndTime *string `form:"endTime,optional"`
}

// Get order list request params | 固定桩和储能柜充电订单信息列表请求参数
// swagger:model OrderListReq
type OrderListReq struct {
	PageInfo
	// 订单状态：0-全部，1-充电中，2-待结算，3-挂单中，4-已完成，5-已退款
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 订单类型：1-固定桩充电，2-上门送电，3-储能柜充电4-应急救援
	OrderType *int8 `form:"orderType,optional"`
	// 实际开始时间
	StartTime *string `form:"startTime,optional"`
	// 实际结束时间
	EndTime *string `form:"endTime,optional"`
}

// 扫码充电订单信息列表请求参数
// swagger:model ChargeOrderListReq
type ChargeOrderListReq struct {
	PageInfo
	// 订单状态：0-全部，1-充电中，2-待结算，3-挂单中，4-已完成，5-已退款
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 实际开始时间
	StartTime *string `form:"startTime,optional"`
	// 实际结束时间
	EndTime *string `form:"endTime,optional"`
}

// 预约充电订单信息列表请求参数
// swagger:model AppointmentOrderListReq
type AppointmentOrderListReq struct {
	PageInfo
	// 订单状态：0-全部，1-待付款，2-分配中，3-待充电，4-预约成功
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 预约开始时间
	StartTime *string `form:"startTime,optional"`
	// 预约结束时间
	EndTime *string `form:"endTime,optional"`
}

// 上门充电订单信息列表请求参数
// swagger:model DoorOrderListReq
type DoorOrderListReq struct {
	PageInfo
	// 订单状态：0-全部，1-待付款，2-派单中，3-已派单，4-小哥已出发，5-充电中，6-已完成，7-已取消
	OrderStatus *int8 `form:"orderStatus,optional"`
	// 实际开始时间
	StartTime *string `form:"startTime,optional"`
	// 实际结束时间
	EndTime *string `form:"endTime,optional"`
}

// The order information response | Order信息返回体
// swagger:model OrderInfoResp
type OrderInfoResp struct {
	BaseDataInfo
	// order information | Order信息数据
	Data OrderInfoHistory `json:"data"`
}

type OrderInfoHistory struct {
	OrderInfo
	VehicleModelInfo *VehicleModelInfo `json:"vehicleModelInfo"`
	StatusHistory    []OrderHistory    `json:"statusHistory"`
	BeforeImage      []string          `json:"beforeImage"`
	AfterImage       []string          `json:"afterImage"`
}

type OrderHistory struct {
	PreviousStatus *int8   `json:"previousStatus"`
	CurrentStatus  *int8   `json:"currentStatus"`
	OperatorType   *int8   `json:"operatorType"`
	OperatorName   *string `json:"operatorName"`
	Memo           *string `json:"memo"`
	OperationTime  string  `json:"operationTime"`
}

// swagger:model DeleteOrderIdsReq
type DeleteOrderIdsReq struct {
	OrderIds []string `json:"orderIds"`
}

// swagger:model GetOrderByIdReq
type GetOrderByIdReq struct {
	OrderId string `path:"orderId"`
}

// swagger:model CancelOrderReq
type CancelOrderReq struct {
	OrderId      *string `form:"orderId"`
	CancelReason *string `json:"cancelReason,optional"`
}

// swagger:model BaseOrderIdResp
type BaseOrderIdResp struct {
	BaseDataInfo
	Data OrderIdInfo `json:"data"`
}

// swagger:model OrderIdInfo
type OrderIdInfo struct {
	OrderId *string `json:"orderId"`
}

// swagger:model StartChargingOrderIdReq
type StartChargingOrderIdReq struct {
	OrderId *string `json:"orderId"`
}

// swagger:model StopChargingOrderIdReq
type StopChargingOrderIdReq struct {
	OrderId *string `json:"orderId"`
}

// swagger:model StopChargingOrderIdResp
type StopChargingOrderIdResp struct {
	BaseDataInfo
	Data StopChargingInfo `json:"data"`
}

// swagger:model StopChargingInfo
type StopChargingInfo struct {
	ChargingAmount   *int64      `json:"chargingAmount"`
	ChargingDuration *int64      `json:"chargingDuration"`
	TotalAmount      *int64      `json:"totalAmount"`
	PaymentInfo      PaymentInfo `json:"paymentInfo"`
}

// swagger:model PaymentInfo
type PaymentInfo struct {
	PaymentId *string `json:"paumentId"`
	PaymentNo *string `json:"paymentNo"`
}

// swagger:model RateOrderReq
type RateOrderReq struct {
	OrderId *string `path:"orderId"`
	Rating  *int8   `json:"rating"`
	Comment *string `json:"comment,optional"`
	Images  *string `json:"images,optional"`
}

// swagger:model OrderTotalAmountReq
type OrderTotalAmountReq struct {
	OrderIds []string `json:"orderIds"`
	UserId   *string  `json:"userId"`
}

// swagger:model OrderTotalAmountResp
type OrderTotalAmountResp struct {
	TotalAmount *float64 `json:"totalAmount"`
}

// swagger:model AddOrderReq
type AddOrderReq struct {
	ServiceAddress            *string  `json:"serviceAddress"`
	ContactName               *string  `json:"contactName"`
	ContactPhone              *string  `json:"contactPhone"`
	Gender                    *int8    `json:"gender"`
	VehicleDetailAddress      *string  `json:"vehicleDetailAddress,optional"`
	AppointmentTime           string   `json:"appointmentTime"`
	VehicleModelId            *string  `json:"vehicleModelId"`
	VehicleNo                 *string  `json:"vehicleNo"`
	Images                    []string `json:"images,optional"`
	Remark                    *string  `json:"remark,optional"`
	OrderType                 int8     `json:"orderType"`
	Longitude                 *float64 `json:"longitude"`
	Latitude                  *float64 `json:"latitude"`
	EstimatedElec             *int     `json:"estimatedElec"`             // 预估电量
	TotalAmount               *float64 `json:"totalAmount"`               //  订单金额
	EstimatedFee              *float64 `json:"estimatedFee"`              //  预估电费
	EstimatedChargeServiceFee *float64 `json:"estimatedChargeServiceFee"` //  预估服务费
	EstimatedDoorFee          *float64 `json:"estimatedDoorFee"`
}

// swagger:model AddQrOrderReq
type AddQrOrderReq struct {
	OrderType      int8     `json:"orderType"` // 订单类型：1-固定桩充电，3-储能柜充电
	DeviceNo       *string  `json:"deviceNo"`
	GunNo          *string  `json:"gunNo"`
	ServiceAddress *string  `json:"serviceAddress"`
	Longitude      *float64 `json:"longitude"`
	Latitude       *float64 `json:"latitude"`
	TotalAmount    *float64 `json:"totalAmount"` //  订单金额
}

// swagger:model GetStaffHomeOrderResp
type GetStaffHomeOrderResp struct {
	BaseDataInfo
	Data HomeOrderListInfo `json:"data"`
}

// swagger:model HomeOrderListInfo
type HomeOrderListInfo struct {
	BaseListInfo
	// The order list data | Order信息列表数据
	Data []HomeOrderInfo `json:"data"`
}

// swagger:model HomeOrderInfo
type HomeOrderInfo struct {
	OrderId         string  `json:"orderId"`
	OrderType       int8    `json:"orderType"`
	AppointmentTime *int64  `json:"appointmentTime"`
	Address         string  `json:"address"`
	OrderAmount     float64 `json:"orderAmount"`
	Status          int8    `json:"status"`
}

// swagger:model GetStaffOrderAppointTimeReq
type GetStaffOrderAppointTimeReq struct {
	Date string `form:"date,optional"`
}

// swagger:model GetStaffOrderAppointTimeResp
type GetStaffOrderAppointTimeResp struct {
	BaseDataInfo
	Data GetStaffOrderAppointTimeData `json:"data"`
}

type GetStaffOrderAppointTimeData struct {
	Dates []DateInfo `json:"dates"`
	Task  []TaskInfo `json:"task"`
}

// swagger:model DateInfo
type DateInfo struct {
	Desc string `json:"desc"`
	Date string `json:"date"`
	Week string `json:"week"`
}

// swagger:model TaskInfo
type TaskInfo struct {
	StartTime   string   `json:"startTime"`
	EndTime     string   `json:"endTime"`
	TodoCount   int64    `json:"todoCount"`
	FinishCount int64    `json:"finishCount"`
	IsCurrent   bool     `json:"isCurrent"`
	OrderIds    []string `json:"orderIds"`
}

// swagger:model HandleOrderReq
type HandleOrderReq struct {
	OrderId      *string `json:"orderId"`
	HandleType   *int8   `json:"handleType"`
	HandleReason *string `json:"handleReason,optional"`
}

// swagger:model FinishOrderReq
type FinishOrderReq struct {
	OrderId *string  `json:"orderId"`
	Images  []string `json:"images,optional"`
}

// swagger:model ArriveOrderReq
type ArriveOrderReq struct {
	OrderId *string `json:"orderId"`
}

// swagger:model StartOrderReq
type StartOrderReq struct {
	OrderId *string  `json:"orderId"`
	Images  []string `json:"images"`
}

// swagger:model StopOrderReq
type StopOrderReq struct {
	OrderId *string `json:"orderId"`
}

// swagger:model GetStaffOrderDetailReq
type GetStaffOrderDetailReq struct {
	OrderId *string `path:"orderId"`
}

// swagger:model payNoticeReq
type PayNoticeReq struct {
	OrderId       *string  `json:"orderId"`
	PaymentStatus *int8    `json:"paymentStatus"`
	PaymentTime   *int64   `json:"paymentTime"`
	TransactionId *string  `json:"transactionId"`
	Amount        *float64 `json:"amount"`
}

// swagger:model GetStationNearPriceReq
type GetStationNearPriceReq struct {
	Lat   *float64 `json:"lat"`
	Lon   *float64 `json:"lon"`
	Types *int8    `json:"types"`
	Elec  *int8    `json:"elec"`
}

// swagger:model GetStationNearPriceResp
type GetStationNearPriceResp struct {
	BaseDataInfo
	Data GetStationNearPriceData `json:"data"`
}

type GetStationNearPriceData struct {
	EstimatedFee              float64  `json:"estimatedFee"`
	EstimatedChargeServiceFee float64  `json:"estimatedChargeServiceFee"`
	EstimatedDoorFee          *float64 `json:"estimatedDoorFee"`
	EmergencyFee              *float64 `json:"emergencyFee"`
}

// swagger:model OrderPayReq
type OrderPayReq struct {
	OrderId       *string `json:"orderId"`
	PaymentMethod *int64  `json:"paymentMethod"`
	ClientIp      *string `json:"clientIp,optional"`
}

// swagger:model OrderPayResp
type OrderPayResp struct {
	BaseDataInfo
	Data WxResponseInfo `json:"data"`
}

// swagger:model WxResponseInfo
type WxResponseInfo struct {
	WxPaymentParams WxPaymentResp `json:"wxPaymentParams"`
	PaymentId       string        `json:"paymentId"`
	PaymentNo       string        `json:"paymentNo"`
}

// swagger:model WxPaymentResp
type WxPaymentResp struct {
	AppId     string `json:"appId"`
	NonceStr  string `json:"nonceStr"`
	PaySign   string `json:"paySign"`
	Package   string `json:"package"`
	Timestamp string `json:"timestamp"`
	SignType  string `json:"signType"`
}

// swagger:model AllocationOrderReq
type AllocationOrderReq struct {
	StaffId   string `json:"staffId"`
	OrderId   string `json:"orderId"`
	GunId     string `json:"gunId"`
	GunNumber string `json:"gunNumber"`
	DeviceId  string `json:"deviceId"`
	DeviceNo  string `json:"deviceNo"`
}

// swagger:model CloseOrderReq
type CloseOrderReq struct {
	OrderId        *string  `json:"orderId"`
	PromotionId    *string  `json:"promotionId,optional"`
	DiscountAmount *float64 `json:"discountAmount,optional"`
}

// swagger:model OperateOrderReq
type OperateOrderReq struct {
	OrderNo     *string  `json:"orderNo"`
	DeviceNo    *string  `json:"deviceNo"`
	GunNo       *string  `json:"gunNo"`
	Amount      *float64 `json:"amount"`
	PhoneNum    *string  `json:"phoneNum,optional"`
	OuterUserId *string  `json:"outerUserId,optional"`
}

// swagger:model OperateOrderStartReq
type OperateOrderStartReq struct {
	OrderNo *string `json:"orderNo"`
}

// swagger:model OperateOrderPushReq
type OperateOrderPushReq struct {
	OrderNo *string `json:"orderNo"`
}

// The data of tb charging record information | TbChargingRecord信息
// swagger:model TbChargingRecordInfo
type TbChargingRecordInfo struct {
	// 记录ID，主键
	RecordId *string `json:"recordId,optional"`
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 充电枪ID
	GunId *string `json:"gunId,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 车辆ID
	VehicleId *string `json:"vehicleId,optional"`
	// 开始时间
	StartTime *int64 `json:"startTime,optional"`
	// 结束时间
	EndTime *int64 `json:"endTime,optional"`
	// 开始SOC(%)
	StartSoc *float64 `json:"startSoc,optional"`
	// 结束SOC(%)
	EndSoc *float64 `json:"endSoc,optional"`
	// 充电电量(kWh)
	ChargingAmount *float64 `json:"chargingAmount,optional"`
	// 峰值功率(kW)
	PeakPower *float64 `json:"peakPower,optional"`
	// 平均功率(kW)
	AveragePower *float64 `json:"averagePower,optional"`
	// 充电时长(分钟)
	ChargingDuration *int32 `json:"chargingDuration,optional"`
	// 电费(元)
	ElectricityFee *float64 `json:"electricityFee,optional"`
	// 服务费(元)
	ServiceFee *float64 `json:"serviceFee,optional"`
	// 总费用(元)
	TotalFee *float64 `json:"totalFee,optional"`
	// 状态：0-异常，1-正常
	Status *int8 `json:"status,optional"`
	// 停止原因：1-正常充满，2-用户停止，3-设备故障，4-断电，5-超时
	StopReason *int8 `json:"stopReason,optional"`
	// 备注
	Memo *string `json:"memo,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tb charging record list | TbChargingRecord信息列表数据
// swagger:model TbChargingRecordListResp
type TbChargingRecordListResp struct {
	BaseDataInfo
	// The tb charging record list data | TbChargingRecord信息列表数据
	Data TbChargingRecordListInfo `json:"data"`
}

// The tb charging record list data | TbChargingRecord信息列表数据
// swagger:model TbChargingRecordListInfo
type TbChargingRecordListInfo struct {
	BaseListInfo
	// The tb charging record list data | TbChargingRecord信息列表数据
	Data []TbChargingRecordInfo `json:"data"`
}

// Get tb charging record list request params | TbChargingRecord信息列表请求参数
// swagger:model TbChargingRecordListReq
type TbChargingRecordListReq struct {
	PageInfo
	// 记录ID，主键
	RecordId *string `json:"recordId,optional"`
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 充电枪ID
	GunId *string `json:"gunId,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 车辆ID
	VehicleId *string `json:"vehicleId,optional"`
	// 开始时间
	StartTime *int64 `json:"startTime,optional"`
	// 结束时间
	EndTime *int64 `json:"endTime,optional"`
	// 开始SOC(%)
	StartSoc *float64 `json:"startSoc,optional"`
	// 结束SOC(%)
	EndSoc *float64 `json:"endSoc,optional"`
	// 充电电量(kWh)
	ChargingAmount *float64 `json:"chargingAmount,optional"`
	// 峰值功率(kW)
	PeakPower *float64 `json:"peakPower,optional"`
	// 平均功率(kW)
	AveragePower *float64 `json:"averagePower,optional"`
	// 充电时长(分钟)
	ChargingDuration *int32 `json:"chargingDuration,optional"`
	// 电费(元)
	ElectricityFee *float64 `json:"electricityFee,optional"`
	// 服务费(元)
	ServiceFee *float64 `json:"serviceFee,optional"`
	// 总费用(元)
	TotalFee *float64 `json:"totalFee,optional"`
	// 状态：0-异常，1-正常
	Status *int8 `json:"status,optional"`
	// 停止原因：1-正常充满，2-用户停止，3-设备故障，4-断电，5-超时
	StopReason *int8 `json:"stopReason,optional"`
	// 备注
	Memo *string `json:"memo,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The tb charging record information response | TbChargingRecord信息返回体
// swagger:model TbChargingRecordInfoResp
type TbChargingRecordInfoResp struct {
	BaseDataInfo
	// tb charging record information | TbChargingRecord信息数据
	Data TbChargingRecordInfo `json:"data"`
}

// swagger:model DeleteTbChargingRecordIdsReq
type DeleteTbChargingRecordIdsReq struct {
	RecordIds []string `json:"recordIds"`
}

// swagger:model GetTbChargingRecordByIdReq
type GetTbChargingRecordByIdReq struct {
	RecordId string `path:"recordId"`
}

// swagger:model DeviceChargingStatsReq
type DeviceChargingStatsReq struct {
	DeviceId *string `path:"deviceId"`
}

// swagger:model DeviceChargingStatsResp
type DeviceChargingStatsResp struct {
	BaseDataInfo
	Data ChargingInfo `json:"data"`
}

// swagger:model ChargingInfo
type ChargingInfo struct {
	ChargingCount    int64   `json:"chargingCount" db:"chargingCount"`
	ChargingAmount   float64 `json:"chargingAmount" db:"chargingAmoun"`
	ChargingDuration int64   `json:"chargingDuration" db:"chargingDuratio"`
}

// The data of tb order complaint information | TbOrderComplaint信息
// swagger:model TbOrderComplaintInfo
type TbOrderComplaintInfo struct {
	// 投诉ID，主键
	ComplaintId *string `json:"complaintId,optional"`
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 商户ID
	MerchantId *string `json:"merchantId,optional"`
	// 投诉类型：1-服务质量，2-价格争议，3-设备故障，4-人员态度，5-其他
	ComplaintType *int8 `json:"complaintType,optional"`
	// 投诉内容
	ComplaintContent *string `json:"complaintContent,optional"`
	// 附件URL，多个用逗号分隔
	AttachmentUrls *string `json:"attachmentUrls,optional"`
	// 状态：0-待处理，1-处理中，2-已解决，3-已关闭
	Status *int8 `json:"status,optional"`
	// 处理人ID
	HandlerId *string `json:"handlerId,optional"`
	// 处理人姓名
	HandlerName *string `json:"handlerName,optional"`
	// 处理意见
	HandlingOpinion *string `json:"handlingOpinion,optional"`
	// 处理结果
	HandlingResult *string `json:"handlingResult,optional"`
	// 处理时间
	HandlingTime *int64 `json:"handlingTime,optional"`
	// 用户反馈
	UserFeedback *string `json:"userFeedback,optional"`
	// 满意度：1-5星
	Satisfaction *int8 `json:"satisfaction,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tb order complaint list | TbOrderComplaint信息列表数据
// swagger:model TbOrderComplaintListResp
type TbOrderComplaintListResp struct {
	BaseDataInfo
	// The tb order complaint list data | TbOrderComplaint信息列表数据
	Data TbOrderComplaintListInfo `json:"data"`
}

// The tb order complaint list data | TbOrderComplaint信息列表数据
// swagger:model TbOrderComplaintListInfo
type TbOrderComplaintListInfo struct {
	BaseListInfo
	// The tb order complaint list data | TbOrderComplaint信息列表数据
	Data []TbOrderComplaintInfo `json:"data"`
}

// Get tb order complaint list request params | TbOrderComplaint信息列表请求参数
// swagger:model TbOrderComplaintListReq
type TbOrderComplaintListReq struct {
	PageInfo
	// 状态：0-待处理，1-处理中，2-已解决，3-已关闭
	Status *int8 `form:"status,optional"`
}

// The tb order complaint information response | TbOrderComplaint信息返回体
// swagger:model TbOrderComplaintInfoResp
type TbOrderComplaintInfoResp struct {
	BaseDataInfo
	// tb order complaint information | TbOrderComplaint信息数据
	Data TbOrderComplaintInfo `json:"data"`
}

// swagger:model DeleteTbOrderComplaintIdsReq
type DeleteTbOrderComplaintIdsReq struct {
	ComplaintIds []string `json:"complainIds"`
}

// swagger:model GetTbOrderComplaintByIdReq
type GetTbOrderComplaintByIdReq struct {
	ComplaintId string `path:"complaintId"`
}

// swagger:model SubmitFeedbackReq
type SubmitFeedbackReq struct {
	ComplaintId  *string `json:"complaintId"`
	UserFeedback *string `json:"userFeedback"`
	Satisfaction *int8   `json:"satisfaction"`
}

// swagger:model OrderComplaintReq
type OrderComplaintReq struct {
	OrderId          *string `json:"orderId"`
	ComplaintType    *int8   `json:"complaintType,optional"`
	ComplaintContent *string `json:"complaintContent,optional"`
	AttachmentUrls   *string `json:"attachmentUrls,optional"`
}

// swagger:model OrderComplaintResp
type OrderComplaintResp struct {
	BaseDataInfo
	Data ComplaintIdInfo `json:"data"`
}

// swagger:model ComplaintIdInfo
type ComplaintIdInfo struct {
	ComplaintId *string `json:"complaintId"`
}

// The data of tb order status log information | TbOrderStatusLog信息
// swagger:model TbOrderStatusLogInfo
type TbOrderStatusLogInfo struct {
	LogId *string `json:"logId,optional"`
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 前一状态
	PreviousStatus *int8 `json:"previousStatus,optional"`
	// 当前状态
	CurrentStatus *int8 `json:"currentStatus,optional"`
	// 操作者类型：1-用户，2-商户，3-服务人员，4-系统
	OperatorType *int8 `json:"operatorType,optional"`
	// 操作者ID
	OperatorId *string `json:"operatorId,optional"`
	// 操作者名称
	OperatorName *string `json:"operatorName,optional"`
	// 操作时间
	OperationTime *int64 `json:"operationTime,optional"`
	// 备注
	Memo *string `json:"memo,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
}

// The response data of tb order status log list | TbOrderStatusLog信息列表数据
// swagger:model TbOrderStatusLogListResp
type TbOrderStatusLogListResp struct {
	BaseDataInfo
	// The tb order status log list data | TbOrderStatusLog信息列表数据
	Data TbOrderStatusLogListInfo `json:"data"`
}

// The tb order status log list data | TbOrderStatusLog信息列表数据
// swagger:model TbOrderStatusLogListInfo
type TbOrderStatusLogListInfo struct {
	BaseListInfo
	// The tb order status log list data | TbOrderStatusLog信息列表数据
	Data []TbOrderStatusLogInfo `json:"data"`
}

// Get tb order status log list request params | TbOrderStatusLog信息列表请求参数
// swagger:model TbOrderStatusLogListReq
type TbOrderStatusLogListReq struct {
	PageInfo
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 前一状态
	PreviousStatus *int8 `json:"previousStatus,optional"`
	// 当前状态
	CurrentStatus *int8 `json:"currentStatus,optional"`
	// 操作者类型：1-用户，2-商户，3-服务人员，4-系统
	OperatorType *int8 `json:"operatorType,optional"`
	// 操作者ID
	OperatorId *string `json:"operatorId,optional"`
	// 操作者名称
	OperatorName *string `json:"operatorName,optional"`
	// 操作时间
	OperationTime *int64 `json:"operationTime,optional"`
	// 备注
	Memo *string `json:"memo,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
}

// The tb order status log information response | TbOrderStatusLog信息返回体
// swagger:model TbOrderStatusLogInfoResp
type TbOrderStatusLogInfoResp struct {
	BaseDataInfo
	// tb order status log information | TbOrderStatusLog信息数据
	Data TbOrderStatusLogInfo `json:"data"`
}

// swagger:model StaffOrdersReq
type StaffOrdersReq struct {
	StaffId string `path:"staffId"`
}

// swagger:model StaffOrdersResp
type StaffOrdersResp struct {
	BaseDataInfo
	Data StaffOrdersInfo `json:"data"`
}

// swagger:model StaffOrdersInfo
type StaffOrdersInfo struct {
	TodayMoney float64 `json:"todayMoney"`
	TodayOrder int64   `json:"todayOrder"`
	TodayElec  float64 `json:"todayElec"`
}
