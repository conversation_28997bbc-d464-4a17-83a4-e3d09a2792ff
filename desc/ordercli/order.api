import "../base.api"

type (
    // The data of order information | Order信息
    OrderInfo {
        // 订单ID，主键 
        OrderId  *string `json:"orderId,optional"`

        // 订单编号 
        OrderNo  *string `json:"orderNo,optional"`

        // 用户ID 
        UserId  *string `json:"userId,optional"`

        // 设备ID 
        DeviceId  *string `json:"deviceId,optional"`

        // 设备编号
        DeviceNo *string `json:"deviceNo,optional"`

        // 充电枪ID 
        GunId  *string `json:"gunId,optional"`

        // 充电枪编号
        GunNo *string `json:"gunNo,optional"`

        // 服务人员ID 
        StaffId  *string `json:"staffId,optional"`

        // 车辆ID 
        VehicleId  *string `json:"vehicleId,optional"`

        // 商户ID 
        MerchantId  *string `json:"merchantId,optional"`

        // 充电站ID 
        StationId  *string `json:"stationId,optional"`

        // 订单类型：1-固定桩充电，2-上门充电，3-储能柜充电 
        OrderType  *int8 `json:"orderType,optional"`

        // 订单状态：0-待支付，1-支付中，2-待服务，3-服务中，4-已完成，5-已取消，6-已退款 
        OrderStatus  *int8 `json:"orderStatus,optional"`

        // 服务状态：0-待分配，1-已分配，2-前往中，3-服务中，4-已完成 
        ServiceStatus  *int8 `json:"serviceStatus,optional"`

        // 充电状态：0-未开始，1-充电中，2-已完成，3-异常中断 
        ChargingStatus  *int8 `json:"chargingStatus,optional"`

        // 预约时间 
        AppointmentTime  *int64 `json:"appointmentTime,optional"`

        // 实际开始时间 
        StartTime  *int64 `json:"startTime,optional"`

        // 实际结束时间 
        EndTime  *int64 `json:"endTime,optional"`

        // 服务地址 
        ServiceAddress  *string `json:"serviceAddress,optional"`

        // 服务位置经度 
        Longitude  *float64 `json:"longitude,optional"`

        // 服务位置纬度 
        Latitude  *float64 `json:"latitude,optional"`

        // 充电电量(kWh) 
        ChargingAmount  *float64 `json:"chargingAmount,optional"`

        // 充电时长(分钟) 
        ChargingDuration  *int32 `json:"chargingDuration,optional"`

        // 电费(元) 
        ElectricityFee  *float64 `json:"electricityFee,optional"`

        // 服务费(元) 
        ServiceFee  *float64 `json:"serviceFee,optional"`

        // 时长费(元) 
        TimeFee  *float64 `json:"timeFee,optional"`

        // 优惠金额(元) 
        DiscountAmount  *float64 `json:"discountAmount,optional"`

        // 订单总金额(元) 
        TotalAmount  *float64 `json:"totalAmount,optional"`

        // 支付状态：0-未支付，1-已支付，2-已退款 
        PaymentStatus  *int8 `json:"paymentStatus,optional"`

        // 支付时间 
        PaymentTime  *int64 `json:"paymentTime,optional"`

        // 支付方式：1-微信支付，2-支付宝，3-余额支付 
        PaymentMethod  *int8 `json:"paymentMethod,optional"`

        // 支付交易号 
        TransactionId  *string `json:"transactionId,optional"`

        // 取消原因 
        CancelReason  *string `json:"cancelReason,optional"`

        // 取消时间 
        CancelTime  *int64 `json:"cancelTime,optional"`

        // 退款时间 
        RefundTime  *int64 `json:"refundTime,optional"`

        // 退款金额(元) 
        RefundAmount  *float64 `json:"refundAmount,optional"`

        // 评价分数：1-5星 
        Rating  *int8 `json:"rating,optional"`

        // 评价内容 
        Comment  *string `json:"comment,optional"`

        // 评价时间 
        CommentTime  *int64 `json:"commentTime,optional"`

        // 商户收入(元) 
        MerchantIncome  *float64 `json:"merchantIncome,optional"`

        // 平台收入(元) 
        PlatformIncome  *float64 `json:"platformIncome,optional"`

        // 结算状态：0-未结算，1-已结算 
        SettlementStatus  *int8 `json:"settlementStatus,optional"`

        // 结算时间 
        SettlementTime  *int64 `json:"settlementTime,optional"`

        // 开票状态：0-未开票，1-已开票 
        InvoiceStatus  *int8 `json:"invoiceStatus,optional"`

        // 开始SOC(%) 
        StartSoc  *string `json:"startSoc,optional"`

        // 结束SOC(%) 
        EndSoc  *string `json:"endSoc,optional"`

        // 备注 
        Memo  *string `json:"memo,optional"`
        Images *string `json:"images,optional"`

        EstimatedElec *int `json:"estimatedElec,optional"`

        ContactName *string `json:"contactName,optional"`
        ContactPhone *string `json:"contactPhone,optional"`
        Gender *int8 `json:"gender,optional"`
        VehicleDetailAddress *string `json:"vehicleDetailAddress,optional"`
        VehicleNo *string `json:"vehicleNo,optional"`
        DoorServiceFee *float64 `json:"doorServiceFee,optional"`
        EstimatedFee *float64 `json:"estimatedFee,optional"`

        // 创建时间 
        CreateTime  *int64 `json:"createTime,optional"`

        // 更新时间 
        UpdateTime  *int64 `json:"updateTime,optional"`
    }

    // The response data of order list | Order信息列表数据
    OrderListResp {
        BaseDataInfo

        // The order list data | Order信息列表数据
        Data OrderListInfo `json:"data"`
    }

    // The order list data | Order信息列表数据
    OrderListInfo {
        BaseListInfo

        // The order list data | Order信息列表数据
        Data  []OrderInfo  `json:"data"`
    }

    // Get order list request params | 小哥订单信息列表请求参数
        StaffOrderListReq {
            PageInfo

            OrderType *int8 `form:"orderType,optional"`
            // 订单状态
            OrderStatus  *int8 `form:"orderStatus,optional"`
            // 实际开始时间
            StartTime  *string `form:"startTime,optional"`
            // 实际结束时间
            EndTime  *string `form:"endTime,optional"`
            // 服务状态
            ServiceStatus *int8 `form:"serviceStatus,optional"`
        }

    OrderListInternalReq {
        PageInfo
        // 用户ID
        UserId  *string `form:"userId,optional"`
        // 订单类型
        OrderType  *int8 `form:"orderType,optional"`
        // 订单状态
        OrderStatus  *int8 `form:"orderStatus,optional"`
        // 实际开始时间
        StartTime  *string `form:"startTime,optional"`
        // 实际结束时间
        EndTime  *string `form:"endTime,optional"`
    }

    // Get order list request params | 固定桩和储能柜充电订单信息列表请求参数
    OrderListReq {
        PageInfo
        // 订单状态：0-全部，1-充电中，2-待结算，3-挂单中，4-已完成，5-已退款
        OrderStatus  *int8 `form:"orderStatus,optional"`
        // 订单类型：1-固定桩充电，2-上门送电，3-储能柜充电4-应急救援
        OrderType  *int8 `form:"orderType,optional"`
        // 实际开始时间 
        StartTime  *string `form:"startTime,optional"`
        // 实际结束时间 
        EndTime  *string `form:"endTime,optional"`
    }

    // 扫码充电订单信息列表请求参数
    ChargeOrderListReq {
        PageInfo
        // 订单状态：0-全部，1-充电中，2-待结算，3-挂单中，4-已完成，5-已退款
        OrderStatus  *int8 `form:"orderStatus,optional"`
        // 实际开始时间
        StartTime  *string `form:"startTime,optional"`
        // 实际结束时间
        EndTime  *string `form:"endTime,optional"`
    }

    // 预约充电订单信息列表请求参数
    AppointmentOrderListReq {
        PageInfo
        // 订单状态：0-全部，1-待付款，2-分配中，3-待充电，4-预约成功
        OrderStatus  *int8 `form:"orderStatus,optional"`
        // 预约开始时间
        StartTime  *string `form:"startTime,optional"`
        // 预约结束时间
        EndTime  *string `form:"endTime,optional"`
    }

    // 上门充电订单信息列表请求参数
    DoorOrderListReq {
        PageInfo
        // 订单状态：0-全部，1-待付款，2-派单中，3-已派单，4-小哥已出发，5-充电中，6-已完成，7-已取消
        OrderStatus  *int8 `form:"orderStatus,optional"`
        // 实际开始时间
        StartTime  *string `form:"startTime,optional"`
        // 实际结束时间
        EndTime  *string `form:"endTime,optional"`
    }

    // The order information response | Order信息返回体
    OrderInfoResp {
        BaseDataInfo

        // order information | Order信息数据
        Data OrderInfoHistory `json:"data"`
    }

    OrderInfoHistory {
        OrderInfo
        VehicleModelInfo *VehicleModelInfo `json:"vehicleModelInfo"`
        StatusHistory []OrderHistory `json:"statusHistory"`
        BeforeImage []string `json:"beforeImage"`
        AfterImage []string `json:"afterImage"`
    }

    OrderHistory {
        PreviousStatus *int8 `json:"previousStatus"`
        CurrentStatus *int8 `json:"currentStatus"`
        OperatorType *int8 `json:"operatorType"`
        OperatorName *string `json:"operatorName"`
        Memo *string `json:"memo"`
        OperationTime string `json:"operationTime"`
    }
    
    DeleteOrderIdsReq {
        OrderIds []string `json:"orderIds"`
    }

    GetOrderByIdReq {
        OrderId string `path:"orderId"`
    }

    CancelOrderReq {
        OrderId *string `form:"orderId"`
        CancelReason *string `json:"cancelReason,optional"`
    }

    BaseOrderIdResp {
        BaseDataInfo
        Data OrderIdInfo `json:"data"`
    }

    OrderIdInfo {
        OrderId *string `json:"orderId"`
    }

    StartChargingOrderIdReq {
        OrderId *string `json:"orderId"`
    }

    StopChargingOrderIdReq {
        OrderId *string `json:"orderId"`
    }

    StopChargingOrderIdResp {
        BaseDataInfo
        Data StopChargingInfo `json:"data"`
    }

    StopChargingInfo {
        ChargingAmount *int64 `json:"chargingAmount"`
        ChargingDuration *int64 `json:"chargingDuration"`
        TotalAmount *int64 `json:"totalAmount"`
        PaymentInfo PaymentInfo `json:"paymentInfo"`
    }

    PaymentInfo {
        PaymentId *string `json:"paumentId"`
        PaymentNo *string `json:"paymentNo"`
    }

    RateOrderReq {
        OrderId *string `path:"orderId"`
        Rating *int8 `json:"rating"`
        Comment *string `json:"comment,optional"`
        Images *string `json:"images,optional"`
    }

    OrderTotalAmountReq {
        OrderIds []string `json:"orderIds"`
        UserId *string `json:"userId"`
    }

    OrderTotalAmountResp  {
        TotalAmount *float64 `json:"totalAmount"`
    }

    AddOrderReq {
        ServiceAddress *string `json:"serviceAddress"`
        ContactName *string `json:"contactName"`
        ContactPhone *string `json:"contactPhone"`
        Gender *int8 `json:"gender"`
        VehicleDetailAddress *string `json:"vehicleDetailAddress,optional"`
        AppointmentTime string `json:"appointmentTime"`
        VehicleModelId *string `json:"vehicleModelId"`
        VehicleNo *string `json:"vehicleNo"`
        Images []string `json:"images,optional"`
        Remark *string `json:"remark,optional"` 
        OrderType int8 `json:"orderType"`
        longitude *float64 `json:"longitude"`
        Latitude *float64 `json:"latitude"`
        EstimatedElec *int `json:"estimatedElec"`  // 预估电量
        TotalAmount *float64 `json:"totalAmount"`  //  订单金额
        EstimatedFee *float64 `json:"estimatedFee"`  //  预估电费
        EstimatedChargeServiceFee *float64 `json:"estimatedChargeServiceFee"`  //  预估服务费
        EstimatedDoorFee *float64 `json:"estimatedDoorFee"`
    }

    AddQrOrderReq {
        OrderType int8 `json:"orderType"` // 订单类型：1-固定桩充电，3-储能柜充电
        DeviceNo  *string `json:"deviceNo"`
        GunNo *string `json:"gunNo"`
        ServiceAddress *string `json:"serviceAddress"`
        Longitude *float64 `json:"longitude"`
        Latitude *float64 `json:"latitude"`
        TotalAmount *float64 `json:"totalAmount"`  //  订单金额
    }

    GetStaffHomeOrderResp {
        BaseDataInfo
        Data HomeOrderListInfo `json:"data"`
    }

     HomeOrderListInfo {
        BaseListInfo
        // The order list data | Order信息列表数据
        Data  []HomeOrderInfo  `json:"data"`
    }

    HomeOrderInfo {
        OrderId string `json:"orderId"`
        OrderType int8 `json:"orderType"`
        AppointmentTime *int64 `json:"appointmentTime"`
        Address string `json:"address"`
        OrderAmount float64 `json:"orderAmount"`
        Status int8 `json:"status"`
    }

    GetStaffOrderAppointTimeReq {
        Date string `form:"date,optional"`
    }

    GetStaffOrderAppointTimeResp {
        BaseDataInfo
        Data GetStaffOrderAppointTimeData `json:"data"`
    }

    GetStaffOrderAppointTimeData {
        Dates []DateInfo `json:"dates"`
        Task []TaskInfo `json:"task"`
    }

    DateInfo {
        Desc string `json:"desc"`
        Date string `json:"date"`
        Week string `json:"week"`
    }

    TaskInfo {
        StartTime string `json:"startTime"`
        EndTime string `json:"endTime"`
        TodoCount int64 `json:"todoCount"`
        FinishCount int64 `json:"finishCount"`
        IsCurrent bool `json:"isCurrent"`
        OrderIds []string `json:"orderIds"`
    }

    HandleOrderReq {
        OrderId *string `json:"orderId"`
        HandleType *int8 `json:"handleType"`
        HandleReason *string `json:"handleReason,optional"`
    }

    FinishOrderReq {
        OrderId *string `json:"orderId"`
        Images []string `json:"images,optional"`
    }

    ArriveOrderReq {
        OrderId *string `json:"orderId"`
    }

    StartOrderReq {
        OrderId *string `json:"orderId"`
        Images []string `json:"images"`
    }

    StopOrderReq {
        OrderId *string `json:"orderId"`
    }

    GetStaffOrderDetailReq {
        OrderId *string `path:"orderId"`
    }

    payNoticeReq {
        OrderId *string `json:"orderId"`
        PaymentStatus *int8 `json:"paymentStatus"`
        PaymentTime *int64 `json:"paymentTime"`
        TransactionId *string `json:"transactionId"`
        Amount *float64 `json:"amount"`
    }

    GetStationNearPriceReq {
        Lat *float64 `json:"lat"`
        Lon *float64 `json:"lon"`
        Types *int8 `json:"types"`
        Elec *int8 `json:"elec"`
    }

    GetStationNearPriceResp {
        BaseDataInfo
        Data GetStationNearPriceData `json:"data"`
    }

    GetStationNearPriceData {
        EstimatedFee float64 `json:"estimatedFee"`
        EstimatedChargeServiceFee float64 `json:"estimatedChargeServiceFee"`
        EstimatedDoorFee *float64 `json:"estimatedDoorFee"`
        EmergencyFee *float64 `json:"emergencyFee"`
    }

    OrderPayReq {
        OrderId *string `json:"orderId"`
        PaymentMethod *int64 `json:"paymentMethod"`
        ClientIp *string `json:"clientIp,optional"`
    }

    OrderPayResp {
            BaseDataInfo
            Data WxResponseInfo `json:"data"`
        }

        WxResponseInfo {
            WxPaymentParams WxPaymentResp `json:"wxPaymentParams"`
            PaymentId string `json:"paymentId"`
            PaymentNo string  `json:"paymentNo"`
        }

        WxPaymentResp {
            AppId string `json:"appId"`
            NonceStr string `json:"nonceStr"`
            PaySign string `json:"paySign"`
            Package string `json:"package"`
            Timestamp string `json:"timestamp"`
            SignType string `json:"signType"`
        }

    AllocationOrderReq {
        StaffId string `json:"staffId"`
        OrderId string `json:"orderId"`
        GunId string `json:"gunId"`
        GunNumber string `json:"gunNumber"`
        DeviceId string `json:"deviceId"`
        DeviceNo string `json:"deviceNo"`
    }

    CloseOrderReq {
        OrderId *string `json:"orderId"`
        PromotionId *string `json:"promotionId,optional"`
        DiscountAmount *float64 `json:"discountAmount,optional"`
    }

    OperateOrderReq {
        OrderNo *string `json:"orderNo"`
        DeviceNo *string `json:"deviceNo"`
        GunNo *string `json:"gunNo"`
        Amount *float64 `json:"amount"`
        PhoneNum *string `json:"phoneNum,optional"`
        OuterUserId *string `json:"outerUserId,optional"`
    }

    OperateOrderStartReq {
        OrderNo *string `json:"orderNo"`
    }

    OperateOrderPushReq {
            OrderNo *string `json:"orderNo"`
        }
)

@server (
    group: order
    prefix: /v1/api
)

service Ordercli {
    @handler startCharging
    post /order/start(StartChargingOrderIdReq) returns (BaseMsgResp)
    @handler getOrderInternelList
    get /order/internel/list (OrderListInternalReq) returns (OrderListResp)
    @handler getAmountByIds
    post /order/totalamount(OrderTotalAmountReq) returns(OrderTotalAmountResp)
    // 运营商订单补充推送
    @handler pushOrderOperate
    post /order/operate/push (OperateOrderPushReq) returns (BaseMsgResp)
}

@server(
    group: order
    prefix: /v1/api
)

service Ordercli {
    // Create order information | 创建Order信息
    @handler createOrder
    post /order/create (AddOrderReq) returns (BaseOrderIdResp)

    // Create order information | 创建扫码Order信息
    @handler createQrOrder
    post /order/create/qr (AddQrOrderReq) returns (BaseOrderIdResp)

    // Update order information | 更新Order信息
    @handler updateOrder
    post /order/update (OrderInfo) returns (BaseMsgResp)

    // Update order information | 更新Order信息
    @handler payNoticeOrder
    post /order/payNotice (payNoticeReq) returns (BaseMsgResp)

    // Delete order information | 删除Order信息
    @handler deleteOrder
    delete /order/delete (DeleteOrderIdsReq) returns (BaseMsgResp)

    // Get order list | 获取充电Order信息列表
    @handler getOrderList
    get /order/list (OrderListReq) returns (OrderListResp)

    // Get order list | 获取扫码充电Order信息列表
    @handler getChargeOrderList
    get /order/charge/list (ChargeOrderListReq) returns (OrderListResp)

    // Get order list | 获取上门充电Order信息列表
    @handler getDoorOrderList
    get /order/door/list (DoorOrderListReq) returns (OrderListResp)

    // Get order list | 获取预约充电Order信息列表
    @handler getAppointmentOrderList
    get /order/appointment/list (AppointmentOrderListReq) returns (OrderListResp)

    // Get order by ID | 通过ID获取Order信息
    @handler getOrderById
    get /order/detail/:orderId (GetOrderByIdReq) returns (OrderInfoResp)

    //取消订单 todo ......
    @handler cancelOrder
    post /order/cancel/:orderId(CancelOrderReq) returns (BaseMsgResp)

    //结束充电
    @handler stopCharge
    post /order/stop(StopChargingOrderIdReq) returns (StopChargingOrderIdResp)

    //评价订单
    @handler rateOrder
    post /order/rate/:orderId(RateOrderReq) returns (BaseMsgResp)

    @handler getStationNearPrice
    post /order/near/price (GetStationNearPriceReq) returns (GetStationNearPriceResp)

    @handler orderPay
    post /order/pay (OrderPayReq) returns (OrderPayResp)

    @handler closeOrder
    post /order/close (CloseOrderReq) returns (BaseMsgResp)
}


@server(
    group: staff
    prefix: /v2/api/staff
    middleware: StaffAuthMiddleware
)

service Ordercli {
    // Get order list | 获取小哥端Order信息列表
    @handler getStaffOrderList
    get /order/list (StaffOrderListReq) returns (OrderListResp)

    //get staff order by service_status | 获取小哥端首页Order信息列表
    @handler getStaffHomeOrder
    get /home/<USER>

    @handler getStaffOrderAppointTime
    get /order/appointTime (GetStaffOrderAppointTimeReq)  returns (GetStaffOrderAppointTimeResp)

    @handler handleOrder
    post /order/status (HandleOrderReq) returns (BaseMsgResp)

    @handler finishOrder
    post /order/finish (FinishOrderReq) returns (BaseMsgResp)

    @handler arriveOrder
    post /order/arrive (ArriveOrderReq) returns (BaseMsgResp)

    @handler startOrder
    post /order/start (StartOrderReq) returns (BaseMsgResp)

    @handler stopOrder
    post /order/stop (StopOrderReq) returns (BaseMsgResp)

    @handler getStaffOrderDetail
    get /order/detail/:orderId (GetStaffOrderDetailReq) returns (OrderInfoResp)


}

@server(
    group: order
    prefix: /admin
)

service Ordercli {
    @handler AllocationOrderHandler
    post /order/allocation (AllocationOrderReq) returns (BaseMsgResp)
}

@server(
    group: order
    prefix: /internal
)

service Ordercli {
    @handler OperateOrderHandler // 运营商订单
    post /order/operate (OperateOrderReq) returns (BaseMsgResp)

    @handler OperateOrderStopHandler // 运营商订单停止充电
    post /order/operate/stop (OperateOrderStartReq) returns (BaseMsgResp)
}