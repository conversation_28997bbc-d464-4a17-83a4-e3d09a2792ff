//	ordercli
//
//	Description: ordercli service
//
//	Schemes: http, https
//	Host: localhost:8180
//	BasePath: /
//	Version: 0.0.1
//	SecurityDefinitions:
//	  Token:
//	    type: apiKey
//	    name: Authorization
//	    in: header
//	Security:
//	  Token:
//	Consumes:
//	  - application/json
//
//	Produces:
//	  - application/json
//
// swagger:meta
package main

import (
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/middleware"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/utils"
	"flag"
	"fmt"
	"github.com/zeromicro/go-zero/core/proc"
	"os"
	"os/signal"
	"syscall"

	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/pkg/constants"
	"codeup.aliyun.com/6343bfe5d02abff84af18069/JingBackend/modules/register"
	"com.xiaojing.order_service/internal/config"
	"com.xiaojing.order_service/internal/handler"
	"com.xiaojing.order_service/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/ordercli.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	server := rest.MustNewServer(c.RestConf, rest.WithCors(c.CROSConf.Address))
	defer server.Stop()

	server.Use(middleware.NewSetUidToCtxMiddleware().Handle)
	server.Use(middleware.NewSetStaffIdToCtxMiddleware().Handle)

	ctx := svc.NewServiceContext(c)
	handler.RegisterHandlers(server, ctx)

	ip := ""
	if c.Mode == "dev" {
		ip = c.EtcdServer.RegisterLocalIp
	} else {
		var err error
		ip, err = utils.GetLocalIP()
		if err != nil {
			panic(err)
		}
	}
	_, err := register.RegisterServiceToEtcd(c.EtcdServer.Endpoints, constants.OrderService, fmt.Sprintf("%s:%d", ip, c.Port), 30)
	if err != nil {
		logx.Errorf("注册order_service to etcd err:%v", err)
	}

	//双清 防止panic 以及人为中断
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	//1.注册服务终止时的清理函数
	proc.AddShutdownListener(func() {
		cleanup(c)
	})

	//2.中断信号
	go func() {
		<-sigChan
		cleanup(c)
		os.Exit(0)
	}()

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}

func cleanup(c config.Config) {
	_ = register.UnRegisterService(c.EtcdServer.Endpoints, constants.PayService)
}
